import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/routes/route_tracker.dart';
import 'package:openinstall_flutter_plugin/openinstall_flutter_plugin.dart';
import 'package:shared_preferences/shared_preferences.dart';

class OpenInstallManager {
  // 私有构造函数
  OpenInstallManager._internal();

  // 单例实例
  static final OpenInstallManager _instance = OpenInstallManager._internal();

  // 获取单例实例
  static OpenInstallManager get instance => _instance;

  // OpenInstall插件实例
  late final OpeninstallFlutterPlugin _openInstallFlutterPlugin;

  // 是否已初始化
  bool _isInitialized = false;
  bool needGoSignUp = false;

  // 初始化OpenInstall
  void initPlatformState() async {
    if (_isInitialized) {
      LogD("📣OpenInstall已经初始化过，跳过重复初始化");
      return;
    }

    try {
      LogD("📣开始初始化OpenInstall");
      _openInstallFlutterPlugin = OpeninstallFlutterPlugin();
      _openInstallFlutterPlugin.setDebug(kDebugMode);
      _openInstallFlutterPlugin.init(_handleInstallData);

      // if (await _isFirstInstall()) {
      //   LogD("📣首次安装，执行install");
      //   _openInstallFlutterPlugin.install(_handleInstallData);
      // }

      _isInitialized = true;
      LogD("📣OpenInstall初始化完成");
    } catch (e) {
      LogD("📣OpenInstall初始化失败: $e");
      rethrow;
    }
  }

  // 是否首次启动OpenInstall
  Future<bool> _isFirstInstall() async {
    final key = "open_install_first_install_key";
    final prefs = await SharedPreferences.getInstance();
    final isFirstInstall = prefs.getBool(key) ?? true;
    if (isFirstInstall) {
      // 如果是首次安装，则更新标记为 false
      await prefs.setBool(key, false);
    }
    return isFirstInstall;
  }

  // 处理安装数据
  Future<void> _handleInstallData(Map<String, Object> data) async {
    LogD("📣收到OpenInstall数据: $data");

    if (kDebugMode) {
      GPEasyLoading.showToast("来自OP消息>> $data", toast: Toast.LENGTH_LONG);
    }

    try {
      final inviteCode = _parseInstallData(data);

      if (inviteCode?.isEmpty ?? true) {
        LogD("📣邀请码为空，跳过处理");
        return;
      }

      _updateConfig(inviteCode!);
      _handleUserRegistration();
    } catch (e) {
      LogD("📣处理OpenInstall数据失败: $e");
    }
  }

  // 解析安装数据
  String? _parseInstallData(Map<String, Object> data) {
    String? inviteCode;
    final jsonString = data['bindData'] as String?;

    if (jsonString?.isNotEmpty ?? false) {
      try {
        final bindData = jsonDecode(jsonString!) as Map<String, dynamic>;
        inviteCode ??= bindData["i"]?.toString();
        inviteCode ??= bindData["inviteCode"]?.toString();
        LogD("📣解析OpenInstall - inviteCode: $inviteCode");
      } catch (e) {
        LogD("📣解析OpenInstall JSON数据失败: $e");
      }
    }

    return inviteCode;
  }

  // 更新配置
  void _updateConfig(String inviteCode) {
    SecureStorageHelper().writeSecureData(LocalStorageKeys.kRegisterInviteCodeKey, inviteCode);
    LogD("📣更新配置成功 - inviteCode: $inviteCode");
  }

  // 处理用户注册
  void _handleUserRegistration() {
    if (getIt<UserCubit>().isLoggedIn) {
      LogD("📣用户已登录，跳过注册流程");
      return;
    }
    final isInSplashRoute = RouteTracker().getCurrentRouteName() == AppRouter.splash;
    if (isInSplashRoute) {
      needGoSignUp = true;
    } else {
      getIt<NavigatorService>().push(AppRouter.routeLogin, arguments: {"isSignUp": true});
    }
  }

  /// 上报用户注册成功
  void reportUserRegistration() {
    if (kIsWeb) return;
    return _openInstallFlutterPlugin.reportRegister();
  }
}
