import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class IconHelper {
  IconHelper._();

  static Widget loadAsset(
    String source, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    bool shouldEnableThemeGradient = false,
    Color? color,
    List<Color>? colors,
    Map<String, String>? headers,
  }) {
    try {
      if (_isNetworkUrl(source)) {
        return _loadNetworkAsset(
          source,
          width: width,
          height: height,
          fit: fit,
          color: color,
          headers: headers,
        );
      }
      return _loadLocalAsset(
        source,
        width: width,
        height: height,
        fit: fit,
        shouldEnableThemeGradient: shouldEnableThemeGradient,
        color: color,
        colors: colors ?? getIt<NavigatorService>().navigatorKey.currentContext!.theme.linearGradientColors,
      );
    } catch (e) {
      debugPrint('Error loading asset: $source - $e');
      return _buildErrorWidget(width, height);
    }
  }

  static bool _isNetworkUrl(String source) {
    return source.startsWith('http://') || source.startsWith('https://');
  }

  static Widget _loadNetworkAsset(
    String url, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    Color? color,
    Map<String, String>? headers,
  }) {
    if (url.toLowerCase().endsWith('.svg')) {
      return SvgPicture.network(
        url,
        width: width,
        height: height,
        fit: fit,
        colorFilter: color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
        headers: headers,
        placeholderBuilder: (context) => _buildLoadingWidget(width, height),
      );
    }

    return CachedNetworkImage(
      imageUrl: url,
      width: width,
      height: height,
      fit: fit,
      color: color,
      httpHeaders: headers ?? const {},
      placeholder: (context, url) => _buildLoadingWidget(width, height),
      errorWidget: (context, url, error) => _buildErrorWidget(width, height),
    );
  }

  static Widget _loadLocalAsset(
    String assetName, {
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    Color? color,
    List<Color> colors = const [],
    bool shouldEnableThemeGradient = false,
  }) {
    if (shouldEnableThemeGradient) {
      assert(assetName.toLowerCase().endsWith('.svg'), 'Asset must be an SVG file ending with .svg');
      assert(colors.isNotEmpty, 'Colors list must not be empty');
      return ShaderMask(
        shaderCallback: (Rect bounds) {
          return LinearGradient(
            colors: colors,
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ).createShader(bounds);
        },
        blendMode: BlendMode.srcIn,
        child: SvgPicture.asset(
          assetName,
          width: width,
          height: height,
          fit: BoxFit.contain,
        ),
      );
    }
    if (assetName.toLowerCase().endsWith('.svg')) {
      return SvgPicture.asset(
        assetName,
        width: width,
        height: height,
        fit: fit,
        colorFilter: color != null ? ColorFilter.mode(color, BlendMode.srcIn) : null,
      );
    }

    return Image.asset(
      assetName,
      width: width,
      height: height,
      fit: fit,
      color: color,
      errorBuilder: (context, error, stackTrace) => _buildErrorWidget(width, height),
    );
  }

  static Widget _buildLoadingWidget(double? width, double? height) {
    return SizedBox(
      width: width,
      height: height,
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
        ),
      ),
    );
  }

  static Widget _buildErrorWidget(double? width, double? height) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Icon(Icons.error_outline, color: Colors.grey),
    );
  }
}
