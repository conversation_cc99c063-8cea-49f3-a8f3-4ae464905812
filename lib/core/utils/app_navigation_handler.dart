import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../features/main/domain/enums/navigation_item.dart';
import '../../features/main/logic/main/main_cubit.dart';
import '../../shared/models/action_model.dart';
import '../../shared/routes/app_router.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

/// 应用导航处理器
/// App navigation handler
///
/// 用于处理应用内的各种导航逻辑，包括页面跳转、Tab切换、外部链接等
/// Handles various navigation logic within the app, including page jumps, tab switching, external links, etc.
class AppNavigationHandler {
  /// 普通路由字典 - jumpUrl 整数值到路由的映射
  /// Dictionary for jumpUrl integer values to routes
  /// Map 格式: jumpUrl -> (route, needsAuth)
  /// Map format: jumpUrl -> (route, needsAuth)
  static final Map<String, (String, bool)> routeDict = {
    '1': (AppRouter.routeDepositMain, true), // 充值主页 - Deposit main page
    '2': (AppRouter.routeInvite, true), // 邀请页面 - Invite page
    '5': (AppRouter.routeMissionCenter, true), // 任务中心 - Mission center
  };

  /// 需要特殊参数处理的路由字典
  /// Special handling for routes that need arguments
  /// Map 格式: jumpUrl -> (route, needsAuth, argumentsBuilder)
  /// Map format: jumpUrl -> (route, needsAuth, argumentsBuilder)
  static final Map<String, (String, bool, Map<String, dynamic> Function())> specialRouteDict = {
    '4': (AppRouter.routeContractApply, true, () => {'mainContractType': InstrumentType.stock}), // 合约申请 - Contract apply
    // 在此处添加未来需要特殊处理的路由
    // Add future special routes here as needed
  };

  /// Tab 导航字典
  /// Special handling for tab navigation
  /// Map 格式: jumpUrl -> (NavigationItem, needsAuth)
  /// Map format: jumpUrl -> (NavigationItem, needsAuth)
  static final Map<String, (NavigationItem, bool)> tabNavigationDict = {
    '3': (NavigationItem.trade, false), // 交易页面 - Trade page
    // 在此处添加未来的 Tab 索引
    // Add future tab indices here as needed
  };

  /// 处理导航跳转
  /// Handle navigation
  ///
  /// [context] - 构建上下文 Build context
  /// [jumpType] - 跳转类型：1=应用内跳转，2=外部链接 Jump type: 1=in-app navigation, 2=external link
  /// [jumpUrl] - 跳转地址或标识 Jump URL or identifier
  /// [requireAuth] - 是否需要认证 Whether authentication is required
  static void handleNavigation(
    BuildContext context, {
    required int? jumpType,
    required String? jumpUrl,
    bool requireAuth = false,
  }) {
    // 参数校验 - Parameter validation
    if (jumpType == null || jumpUrl == null || jumpUrl.isEmpty) return;

    /// 执行导航的内部函数
    /// Internal function to execute navigation
    void navigate() {
      if (jumpType == 1) {
        // 应用内跳转 - In-app navigation

        // 检查是否为 Tab 导航
        // Check if it's a tab navigation
        if (tabNavigationDict.containsKey(jumpUrl)) {
          final (navigationItem, needsAuth) = tabNavigationDict[jumpUrl]!;

          if (needsAuth) {
            // 需要认证的 Tab 切换
            // Tab switching that requires authentication
            AuthUtils.verifyAuth(() => context.read<MainCubit>().selectedNavigationItem(navigationItem));
          } else {
            // 直接切换 Tab
            // Direct tab switching
            context.read<MainCubit>().selectedNavigationItem(navigationItem);
          }
        }
        // 检查是否为需要特殊参数的路由
        // Check if it's a special route that needs arguments
        else if (specialRouteDict.containsKey(jumpUrl)) {
          final (route, needsAuth, argumentsBuilder) = specialRouteDict[jumpUrl]!;
          final arguments = argumentsBuilder();

          if (needsAuth) {
            // 需要认证的特殊路由跳转
            // Special route navigation that requires authentication
            AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(route, arguments: arguments));
          } else {
            // 直接跳转特殊路由
            // Direct special route navigation
            getIt<NavigatorService>().push(route, arguments: arguments);
          }
        }
        // 检查是否为普通路由导航
        // Check if it's a regular route navigation
        else if (routeDict.containsKey(jumpUrl)) {
          final (route, needsAuth) = routeDict[jumpUrl]!;

          if (needsAuth) {
            // 需要认证的普通路由跳转
            // Regular route navigation that requires authentication
            AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(route));
          } else {
            // 直接跳转普通路由
            // Direct regular route navigation
            getIt<NavigatorService>().push(route);
          }
        }
      } else if (jumpType == 2) {
        // 外部链接跳转 - External URL navigation
        if (requireAuth) {
          // 需要认证的外部链接
          // External link that requires authentication
          AuthUtils.verifyAuth(() => launchUrl(Uri.parse(jumpUrl), mode: LaunchMode.externalApplication));
        } else {
          // 直接打开外部链接
          // Direct external link opening
          launchUrl(Uri.parse(jumpUrl), mode: LaunchMode.externalApplication);
        }
      }
    }

    // 如果整体导航需要认证（无论具体路由如何）
    // If the overall navigation requires auth (regardless of specific route)
    if (requireAuth) {
      AuthUtils.verifyAuth(() => navigate());
    } else {
      navigate();
    }
  }

  /// 获取导航动作模型
  /// Get navigation action model
  ///
  /// 根据跳转类型和地址生成对应的动作模型，用于统一处理导航逻辑
  /// Generate corresponding action model based on jump type and URL for unified navigation logic handling
  ///
  /// [jumpType] - 跳转类型 Jump type
  /// [jumpUrl] - 跳转地址 Jump URL
  ///
  /// 返回 [ActionModel] 动作模型
  /// Returns [ActionModel] action model
  static ActionModel getNavigationAction(int? jumpType, String? jumpUrl) {
    // 参数校验 - Parameter validation
    if (jumpType == null || jumpUrl == null) return ActionModel();

    if (jumpType == 1) {
      // 应用内跳转 - In-app navigation

      // Tab 导航处理
      // Tab navigation handling
      if (tabNavigationDict.containsKey(jumpUrl)) {
        final (navigationItem, _) = tabNavigationDict[jumpUrl]!; // 这里不需要认证标志 We don't need the auth flag here
        return ActionModel(actionType: ActionType.switchTab, bottomBarIndex: navigationItem, tabIndex: 0);
      }
      // 需要特殊参数的路由处理
      // Special route handling that needs arguments
      else if (specialRouteDict.containsKey(jumpUrl)) {
        final (route, _, _) = specialRouteDict[jumpUrl]!; // 这里只需要路由名称 We only need the route name here
        return ActionModel(actionType: ActionType.navigate, routeName: route);
      }
      // 普通路由导航处理
      // Regular route navigation handling
      else if (routeDict.containsKey(jumpUrl)) {
        final (route, _) = routeDict[jumpUrl]!; // 这里不需要认证标志 We don't need the auth flag here
        return ActionModel(actionType: ActionType.navigate, routeName: route);
      }
    }

    // 默认返回空的动作模型
    // Return empty action model by default
    return ActionModel();
  }
}
