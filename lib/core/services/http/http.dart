import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/api/network/endpoint/urls.dart';
import 'package:gp_stock_app/core/services/http/models/response_model.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/http/network_error.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/host_util.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';

import 'http_cache_manager.dart';
import 'network_interceptors.dart';

part 'http_config.dart';

/// 忽略打印的接口
const List<String> kIgnoreApiList = [
  ApiEndpoints.getGainDistribution,
  ApiEndpoints.getMarketPlate,
  ApiEndpoints.getPlateList,
  ApiEndpoints.getIndexList,
  ApiEndpoints.getStockInfo,
  ApiEndpoints.getKlineData,
  ApiEndpoints.getAccountInfo,
  ApiEndpoints.getContractSummary,
  ApiEndpoints.getContractSummaryPage,
  ApiEndpoints.getOrderList,
  ApiEndpoints.getPositionList,
  ApiEndpoints.getMarketStatus,
  ApiEndpoints.wangYiCaptcha,
];

class Http {
  Dio? _dio;

  /// 实例化Http
  factory Http() => _instance;

  /// 初始化配置
  static final Http _instance = Http._internal();

  /// 自定义Header
  final httpHeaders = {
    'Accept': 'application/json,*/*',
    'Content-Type': 'application/json',
    if (kDebugMode) 'X-Skip-Encrypt': 1,
    'Accept-Language': 'zh-CN',
    'api-version': 'v1',
  };

  /// 获取基础配置项
  BaseOptions _options() {
    String baseUrl = '${HostUtil().currentHost ?? Urls.baseUrl}/api';
    return BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: HttpConfig.connectTimeout,
      sendTimeout: (kIsWeb && kDebugMode) ? Duration.zero : HttpConfig.sendTimeout,
      receiveTimeout: HttpConfig.receiveTimeout,
      headers: httpHeaders,
      persistentConnection: true,
    );
  }

  /// 通用全局单例，第一次使用时初始化
  Http._internal() {
    if (_dio == null) {
      // 初始化dio
      _dio = Dio(_options());

      // 配置 Keep-Alive
      (_dio!.httpClientAdapter as IOHttpClientAdapter).createHttpClient = () {
        final client = HttpClient()
          ..connectionTimeout = const Duration(seconds: 20)
          ..idleTimeout = const Duration(seconds: 60)
          ..maxConnectionsPerHost = 100;
        return client;
      };

      // 添加重试拦截器
      final retryIgnoredStatusCodes = {400, 401, 403, 404};
      _dio?.interceptors.add(
        RetryInterceptor(
          dio: _dio!,
          logPrint: LogD,
          retries: 3,
          retryEvaluator: (error, attempt) =>
              error.type != DioExceptionType.cancel && !retryIgnoredStatusCodes.contains(error.response?.statusCode),
        ),
      );

      // 通用拦截器
      _dio?.interceptors.add(CommonInterceptors());
      // Logger拦截器
      if (kDebugMode) _dio?.interceptors.add(LogsInterceptors(ignoredUrls: kIgnoreApiList));
    }
  }

  /// mock数据
  ///
  /// [path] 必传|接口路径
  /// [params] 选填|接口入参
  Future<ResponseModel<T>> _mock<T>(
    String path, {
    Map<String, dynamic>? params,
  }) async {
    ResponseModel<T>? responseModel;
    try {
      var responseStr = await rootBundle.loadString(path);
      var response = json.decode(responseStr);
      var responseData = response;
      responseModel = ResponseModel<T>.fromJson(responseData);
    } catch (e) {
      responseModel = ResponseModel(data: null, code: -200, msg: 'mock没有数据了');
    }
    return responseModel;
  }

  /// 发起单次网络请求（不含重试） / Initiates a single HTTP request (without retry).
  ///
  /// [path] 请求 URL 路径，例如 "/api/user/info" / The request URL path, e.g. "/api/user/info"
  /// [method] HTTP 方法，默认为 POST，可选 GET、PUT、DELETE 等 / The HTTP method, defaults to POST; can be GET, PUT, DELETE, etc.
  /// [queryParameters] URL 查询参数，通常用于 GET / URL query parameters, typically for GET requests.
  /// [params] 请求体参数，通常用于 POST/PUT / Request body parameters, usually for POST/PUT.
  /// [needSignIn] 是否需要用户登录，未登录返回 401 / Whether the request requires login; returns 401 if not logged in.
  /// [needShowToast] 请求失败时是否显示 Toast / Whether to show a toast on failure.
  /// [cancelToken] 用于取消请求，例如用户离开页面 / Token to cancel the request, useful when user leaves the page.
  /// [enableCache] 是否启用缓存 / Whether to enable caching.
  /// [cacheKey] 自定义缓存 key，若为空将自动生成 / Custom cache key; if null, an automatic key will be generated.
  /// [cacheType] 缓存类型：本地或内存 / Cache type: persistent local or in-memory.
  /// [cacheExpire] 缓存过期时间 / Cache expiration duration.
  /// [onCacheUpdate] 命中缓存时的回调 / Callback triggered when cached data is hit.
  Future<ResponseModel<T>> _performRequest<T>(
    String path, {
    HttpMethod method = HttpMethod.post,
    Map<String, dynamic>? queryParameters,
    Object? params,
    bool needSignIn = true,
    bool needShowToast = true,
    CancelToken? cancelToken,
    bool enableCache = false,
    String? cacheKey,
    CacheType cacheType = CacheType.temporary,
    Duration? cacheExpire,
    void Function(ResponseModel)? onCacheUpdate,
  }) async {
    ResponseModel<T>? responseModel;
    try {
      // 登录检查
      // Check if user is logged in
      if (needSignIn && !getIt<UserCubit>().state.isLogin) {
        LogD("🍀$path, 请先登录");
        return ResponseModel(data: null, code: 401, msg: "未登录");
      }
      final effectiveCacheKey = cacheKey ?? HttpCacheManager.generateCacheKey(path, queryParameters, params);

      // 如果启用缓存，先从缓存里取
      // If caching is enabled, try to read from cache first
      if (enableCache) {
        // 自动生成缓存 key
        // Generate cache key if not provided

        final cacheData = await HttpCacheManager().get(
          effectiveCacheKey,
          type: cacheType,
          maxAge: cacheExpire,
        );
        if (cacheData != null) {
          responseModel = ResponseModel<T>.fromJson(cacheData);
          onCacheUpdate?.call(responseModel);
        }
      }

      Response? response;
      switch (method) {
        case HttpMethod.get:
          response = await _dio?.get(path, queryParameters: queryParameters, data: params, cancelToken: cancelToken);
          break;
        case HttpMethod.post:
          response = await _dio?.post(path, data: params, cancelToken: cancelToken);
          break;
        case HttpMethod.put:
          response = await _dio?.put(path, data: params, cancelToken: cancelToken);
          break;
        case HttpMethod.delete:
          response = await _dio?.delete(path, data: params, cancelToken: cancelToken);
          break;
        case HttpMethod.patch:
          response = await _dio?.patch(path, data: params, cancelToken: cancelToken);
          break;
        case HttpMethod.mock:
          return await _mock<T>(path, params: params as Map<String, dynamic>?);
      }

      var responseData = response?.data;
      responseModel = ResponseModel<T>.fromJson(responseData);

      // 如果请求成功且启用缓存，则写缓存
      // Save to cache if request is successful and caching is enabled
      if (enableCache && responseModel.isSuccess) {
        HttpCacheManager().set(
          effectiveCacheKey,
          responseData,
          type: cacheType,
        );
      }

      if (!responseModel.isSuccess) {
        // 如果响应不成功，处理错误提示
        // Handle error toast if response is not successful
        if (responseModel.msg != null) {
          _handleErrorToast(responseModel.msg!, path: path, needShowToast: needShowToast);
        }
      }
    } on FormatException catch (e, stack) {
      LogE("⚠️ JSON格式异常: $e\n$stack");
      responseModel = NetException.jsonSerialization.toResponseModel();
    } on DioException catch (e, stack) {
      if (e.type == DioExceptionType.cancel) {
        responseModel = NetException.cancel.toResponseModel();
      } else {
        LogE("⚠️ Dio异常: $e\n$stack");
        responseModel = NetException.network.toResponseModel();
      }
    } catch (e, stack) {
      LogE("⚠️ 未知异常: $e\n$stack");
      responseModel = ResponseModel(code: -999, msg: '未知错误');
    }
    return responseModel;
  }

  /// 发起 HTTP 请求，支持自动重试 / Initiates an HTTP request with optional automatic retry.
  ///
  /// [path] 请求 URL 路径，例如 "/api/user/info" / The request URL path, e.g. "/api/user/info"
  /// [method] HTTP 方法，默认为 POST，可选 GET、PUT、DELETE 等 / The HTTP method, defaults to POST; can be GET, PUT, DELETE, etc.
  /// [queryParameters] URL 查询参数，通常用于 GET / URL query parameters, typically for GET requests.
  /// [params] 请求体参数，通常用于 POST/PUT / Request body parameters, usually for POST/PUT.
  /// [needSignIn] 是否需要用户登录，未登录返回 401 / Whether the request requires login; returns 401 if not logged in.
  /// [shouldRetry] 是否启用重试机制 / Whether to enable retry mechanism.
  /// [needShowToast] 请求失败时是否显示 Toast / Whether to show a toast on failure.
  /// [cancelToken] 用于取消请求，例如用户离开页面 / Token to cancel the request, useful when user leaves the page.
  /// [enableCache] 是否启用缓存 / Whether to enable caching.
  /// [cacheKey] 自定义缓存 key，若为空将自动生成 / Custom cache key; if null, an automatic key will be generated.
  /// [cacheType] 缓存类型：本地或内存 / Cache type: persistent local or in-memory.
  /// [cacheExpire] 缓存过期时间 / Cache expiration duration.
  /// [onCacheUpdate] 命中缓存时的回调 / Callback triggered when cached data is hit.
  /// [maxRetryAttempts] 最大重试次数，默认 3 / Maximum retry attempts, defaults to 3.
  /// [retryDelay] 每次重试间隔时间 / Delay between retries.
  Future<ResponseModel<T>> request<T>(
    String path, {
    HttpMethod method = HttpMethod.post,
    Map<String, dynamic>? queryParameters,
    Object? params,
    bool needSignIn = true,
    bool shouldRetry = false,
    bool needShowToast = true,
    CancelToken? cancelToken,
    bool enableCache = false,
    String? cacheKey,
    CacheType cacheType = CacheType.temporary,
    Duration? cacheExpire,
    void Function(ResponseModel)? onCacheUpdate,
    int maxRetryAttempts = 3,
    Duration retryDelay = const Duration(milliseconds: 500),
  }) async {
    late ResponseModel<T> responseModel;
    if (shouldRetry == false) maxRetryAttempts = 1;
    for (var attempt = 1; attempt <= maxRetryAttempts; attempt++) {
      responseModel = await _performRequest<T>(
        path,
        method: method,
        queryParameters: queryParameters,
        params: params,
        needSignIn: needSignIn,
        needShowToast: needShowToast,
        cancelToken: cancelToken,
        enableCache: enableCache,
        cacheKey: cacheKey,
        cacheType: cacheType,
        cacheExpire: cacheExpire,
        onCacheUpdate: onCacheUpdate,
      );

      if (responseModel.isSuccess) {
        return responseModel;
      } else {
        if (attempt < maxRetryAttempts) {
          LogD("❗ 第 $attempt 次请求失败，code=${responseModel.code}，准备重试...");
          await Future.delayed(retryDelay);
        }
      }
    }

    return responseModel;
  }

  /// 错误提示
  /// Show toast on error
  void _handleErrorToast(String message, {required String path, bool needShowToast = true}) {
    final needShow = !kIgnoreApiList.contains(path) || needShowToast;
    if (needShow) {
      GPEasyLoading.showToast(message);
    }
  }
}
