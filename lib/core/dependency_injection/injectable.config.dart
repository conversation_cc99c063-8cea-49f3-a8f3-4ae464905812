// dart format width=80
// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:get_it/get_it.dart' as _i174;
import 'package:injectable/injectable.dart' as _i526;

import '../../features/account/domain/repository/account_repository.dart'
    as _i104;
import '../../features/account/domain/repository/bank_repository.dart' as _i908;
import '../../features/account/domain/services/account_service.dart' as _i708;
import '../../features/account/domain/services/bank_service.dart' as _i429;
import '../../features/account/logic/account/account_cubit.dart' as _i809;
import '../../features/account/logic/deposit_channel/deposit_channel_cubit.dart'
    as _i1054;
import '../../features/account/logic/otp/otp_cubit.dart' as _i101;
import '../../features/account/logic/proxy_pay_channel/proxy_pay_channel_cubit.dart'
    as _i419;
import '../../features/account_v2/0_home/account_screen_cubit_v2.dart' as _i608;
import '../../features/activity/domain/repository/activity_repository.dart'
    as _i495;
import '../../features/activity/domain/services/activity_service.dart' as _i104;
import '../../features/activity/logic/activity/activity_cubit.dart' as _i416;
import '../../features/chat/logic/chat/chat_cubit.dart' as _i190;
import '../../features/company_news/domain/repository/company_news_repository.dart'
    as _i220;
import '../../features/company_news/domain/services/company_news_service.dart'
    as _i1035;
import '../../features/company_news/logic/news/company_news_cubit.dart' as _i32;
import '../../features/contract/domain/repository/contract_repository.dart'
    as _i512;
import '../../features/contract/domain/services/contract_service.dart'
    as _i1017;
import '../../features/contract/logic/contract/contract_cubit.dart' as _i436;
import '../../features/contract/logic/contract_terminate/contract_terminate_cubit.dart'
    as _i51;
import '../../features/contract/logic/fund_records/fund_records_cubit.dart'
    as _i958;
import '../../features/convert_rate/domain/repository/convert_rate_repository.dart'
    as _i15;
import '../../features/convert_rate/domain/services/convert_rate_service.dart'
    as _i241;
import '../../features/convert_rate/logic/convert_rate/convert_rate_cubit.dart'
    as _i810;
import '../../features/forgot/logic/forgot/forgot_cubit.dart' as _i620;
import '../../features/home/<USER>/repository/home_repository.dart' as _i541;
import '../../features/home/<USER>/services/home_service.dart' as _i677;
import '../../features/home/<USER>/home/<USER>' as _i906;
import '../../features/home/<USER>/home_notification_cubit/home_notification_cubit.dart'
    as _i304;
import '../../features/home/<USER>/news/news_cubit.dart' as _i951;
import '../../features/invite/domain/repository/invite_repository.dart'
    as _i534;
import '../../features/invite/domain/services/invite_service.dart' as _i201;
import '../../features/invite/logic/invite/invite_cubit.dart' as _i1070;
import '../../features/main/logic/main/main_cubit.dart' as _i1007;
import '../../features/market/domain/repositories/market_repository.dart'
    as _i881;
import '../../features/market/domain/service/market_service.dart' as _i216;
import '../../features/market/logic/cubit/index_trade_cubit.dart' as _i947;
import '../../features/market/logic/index_page/index_page_cubit.dart' as _i64;
import '../../features/market/logic/market/market_cubit.dart' as _i360;
import '../../features/market/logic/market_status/market_status_cubit.dart'
    as _i727;
import '../../features/market/logic/search/search_cubit.dart' as _i159;
import '../../features/market/warning/logic/warning_cubit.dart' as _i279;
import '../../features/market/warning/repository/warning_repository.dart'
    as _i233;
import '../../features/market/warning/service/warning_service.dart' as _i262;
import '../../features/market/watch_list/logic/watch_list_cubit.dart' as _i749;
import '../../features/notifications/domain/repository/notifications_repository.dart'
    as _i998;
import '../../features/notifications/domain/services/notifications_service.dart'
    as _i241;
import '../../features/notifications/logic/notifications/notifications_cubit.dart'
    as _i680;
import '../../features/profile/domain/repository/help_repository.dart' as _i724;
import '../../features/profile/domain/repository/mission_activity_repository.dart'
    as _i754;
import '../../features/profile/domain/repository/profile_repository.dart'
    as _i364;
import '../../features/profile/domain/repository/third_party_channel_repository.dart'
    as _i409;
import '../../features/profile/domain/services/help_service.dart' as _i1050;
import '../../features/profile/domain/services/mission_activity_service.dart'
    as _i722;
import '../../features/profile/domain/services/profile_service.dart' as _i770;
import '../../features/profile/domain/services/third_party_channel_service.dart'
    as _i979;
import '../../features/profile/logic/app_info/app_info_cubit.dart' as _i836;
import '../../features/profile/logic/auth_n/auth_n_cubit.dart' as _i597;
import '../../features/profile/logic/help/help_cubit.dart' as _i201;
import '../../features/profile/logic/mission_center/cubit/mission_activity_cubit.dart'
    as _i125;
import '../../features/profile/logic/profile/profile_cubit.dart' as _i99;
import '../../features/profile/logic/third_party_channel/third_party_channel_cubit.dart'
    as _i60;
import '../../features/profile/logic/vip/vip_cubit.dart' as _i410;
import '../../features/sign_in/domain/repository/app_info_repository.dart'
    as _i972;
import '../../features/sign_in/domain/services/app_info_service.dart' as _i1072;
import '../../features/sign_in/logic/sign_in/sign_in_cubit.dart' as _i821;
import '../../shared/logic/account_info/account_info_cubit.dart' as _i1042;
import '../../shared/logic/selected_exchange_cubit/selected_exchange_cubit.dart'
    as _i86;
import '../../shared/logic/sort_color/sort_color_cubit.dart' as _i1;
import '../../shared/routes/navigator_utils.dart' as _i657;
import '../../shared/services/polling/polling_service.dart' as _i1013;
import '../../shared/services/polling/polling_sevice_v2.dart' as _i456;
import '../../shared/services/web_socket/web_scoket_interface.dart' as _i834;
import '../../shared/services/web_socket/web_socket_service.dart' as _i13;
import '../services/app_info_service.dart' as _i680;
import '../services/common_services.dart' as _i928;
import '../services/image_picker/image_picker_repository.dart' as _i531;
import '../services/image_picker/image_picker_service.dart' as _i818;
import '../services/user/user_cubit.dart' as _i85;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  _i174.GetIt init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    gh.factory<_i680.AppInfoService>(() => _i680.AppInfoService());
    gh.factory<_i201.HelpCubit>(() => _i201.HelpCubit());
    gh.factory<_i279.WarningCubit>(() => _i279.WarningCubit());
    gh.factory<_i620.ForgotCubit>(() => _i620.ForgotCubit());
    gh.factory<_i101.OtpCubit>(() => _i101.OtpCubit());
    gh.factory<_i1054.DepositChannelCubit>(() => _i1054.DepositChannelCubit());
    gh.factory<_i419.ProxyPayChannelCubit>(() => _i419.ProxyPayChannelCubit());
    gh.factory<_i809.AccountCubit>(() => _i809.AccountCubit());
    gh.factory<_i86.SelectedExchangeCubit>(() => _i86.SelectedExchangeCubit());
    gh.singleton<_i928.CommonServices>(() => _i928.CommonServices());
    gh.singleton<_i85.UserCubit>(() => _i85.UserCubit());
    gh.singleton<_i190.ChatCubit>(() => _i190.ChatCubit());
    gh.singleton<_i125.MissionActivityCubit>(
        () => _i125.MissionActivityCubit());
    gh.singleton<_i947.IndexTradeCubit>(() => _i947.IndexTradeCubit());
    gh.singleton<_i749.WatchListCubit>(() => _i749.WatchListCubit());
    gh.singleton<_i1007.MainCubit>(() => _i1007.MainCubit());
    gh.singleton<_i608.AccountScreenCubitV2>(
        () => _i608.AccountScreenCubitV2());
    gh.singleton<_i1.SortColorCubit>(() => _i1.SortColorCubit());
    gh.singleton<_i657.NavigatorService>(() => _i657.NavigatorService());
    gh.singleton<_i1013.PollingService>(() => _i1013.PollingService());
    gh.singleton<_i456.PollingServiceV2>(() => _i456.PollingServiceV2());
    gh.factory<_i409.ThirdPartyChannelRepository>(
        () => _i979.ThirdPartyChannelService());
    gh.singleton<_i233.WarningRepository>(() => _i262.WarningService());
    gh.factory<_i512.ContractRepository>(() => _i1017.ContractService());
    gh.factory<_i881.MarketRepository>(() => _i216.MarketService());
    gh.factory<_i104.AccountRepository>(() => _i708.AccountService());
    gh.singleton<_i727.MarketStatusCubit>(
        () => _i727.MarketStatusCubit(gh<_i881.MarketRepository>()));
    gh.factory<_i64.IndexPageCubit>(
        () => _i64.IndexPageCubit(gh<_i881.MarketRepository>()));
    gh.factory<_i159.SearchCubit>(
        () => _i159.SearchCubit(gh<_i881.MarketRepository>()));
    gh.factory<_i360.MarketCubit>(
        () => _i360.MarketCubit(gh<_i881.MarketRepository>()));
    gh.factory<_i958.FundRecordsCubit>(
        () => _i958.FundRecordsCubit(gh<_i512.ContractRepository>()));
    gh.factory<_i51.ContractTerminateCubit>(
        () => _i51.ContractTerminateCubit(gh<_i512.ContractRepository>()));
    gh.factory<_i436.ContractCubit>(
        () => _i436.ContractCubit(gh<_i512.ContractRepository>()));
    gh.singleton<_i1042.AccountInfoCubit>(() => _i1042.AccountInfoCubit(
          gh<_i104.AccountRepository>(),
          gh<_i1013.PollingService>(),
        ));
    gh.factory<_i15.ConvertRateRepository>(() => _i241.ConvertRateService());
    gh.factory<_i495.ActivityRepository>(() => _i104.ActivityService());
    gh.factory<_i972.AppInfoRepository>(() => _i1072.AppInfoService());
    gh.factory<_i531.ImagePickerRepository>(() => _i818.ImagePickerService());
    gh.factory<_i754.MissionActivityRepository>(
        () => _i722.MissionActivityService());
    gh.factory<_i220.CompanyNewsRepository>(() => _i1035.CompanyNewsService());
    gh.factory<_i534.InviteRepository>(() => _i201.InviteService());
    gh.singleton<_i908.BankRepository>(() => _i429.BankService());
    gh.factory<_i998.NotificationsRepository>(
        () => _i241.NotificationsService());
    gh.factory<_i541.HomeRepository>(() => _i677.HomeService());
    gh.singleton<_i60.ThirdPartyChannelCubit>(() =>
        _i60.ThirdPartyChannelCubit(gh<_i409.ThirdPartyChannelRepository>()));
    gh.singleton<_i834.WebSocketService>(() => _i13.WebSocketChannelService());
    gh.factory<_i724.HelpRepository>(() => _i1050.HelpService());
    gh.factory<_i364.ProfileRepository>(() => _i770.ProfileService());
    gh.factory<_i32.CompanyNewsCubit>(
        () => _i32.CompanyNewsCubit(gh<_i220.CompanyNewsRepository>()));
    gh.lazySingleton<_i821.SignInCubit>(
        () => _i821.SignInCubit(gh<_i834.WebSocketService>()));
    gh.singleton<_i680.NotificationsCubit>(
        () => _i680.NotificationsCubit(gh<_i998.NotificationsRepository>()));
    gh.factory<_i1070.InviteCubit>(
        () => _i1070.InviteCubit(gh<_i534.InviteRepository>()));
    gh.singleton<_i836.AppInfoCubit>(
        () => _i836.AppInfoCubit(gh<_i364.ProfileRepository>()));
    gh.singleton<_i99.ProfileCubit>(
        () => _i99.ProfileCubit(gh<_i364.ProfileRepository>()));
    gh.factory<_i810.ConvertRateCubit>(
        () => _i810.ConvertRateCubit(gh<_i15.ConvertRateRepository>()));
    gh.factory<_i304.HomeNotificationCubit>(
        () => _i304.HomeNotificationCubit(gh<_i541.HomeRepository>()));
    gh.factory<_i906.HomeCubit>(
        () => _i906.HomeCubit(gh<_i541.HomeRepository>()));
    gh.factory<_i951.NewsCubit>(
        () => _i951.NewsCubit(gh<_i541.HomeRepository>()));
    gh.factory<_i416.ActivityCubit>(
        () => _i416.ActivityCubit(gh<_i495.ActivityRepository>()));
    gh.factory<_i410.VipCubit>(
        () => _i410.VipCubit(gh<_i754.MissionActivityRepository>()));
    gh.singleton<_i597.AuthNCubit>(() => _i597.AuthNCubit(
          gh<_i531.ImagePickerRepository>(),
          gh<_i364.ProfileRepository>(),
        ));
    return this;
  }
}
