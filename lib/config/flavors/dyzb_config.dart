
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/constants/enums/trading_mode.dart';

import 'app_config.dart';

void setupDYZBConfig() {
  AppConfig(
    flavor: Flavor.dyzb,
    appName: '德盈资本',
    siteId: "83",
    skinStyle: AppSkinStyle.kTemplateC,
    colorSchemeStyle: ColorSchemeStyle.kDefault,
    environment: "test",
    baseUrl: 'https://abdads.qeiqazdy.lknhvfdsal.com',
    marketWsUrl: 'wss://abdads.qeiqazdy.lknhvfdsal.com',
    inviteLinkUrl: 'https://abdads.qeiqazdy.lknhvfdsal.com/#/?inviteCode=',
    currentTradingModel: TradingMode.stockAndFutures,
    // AES encryption key for GP flavor
    encryptionKey: '8JUOEEGjDsmrl30P',
    wangYiCaptchaLoginKey: '7650f145f0824ba6973d99d43a99d15c',
    wangYiCaptchaSMSKey: '0ecb64a2b8cc46d2b53c6e42d08ad708',
    ossUrls: [
      "https://rs-1337543130.cos.ap-shanghai.myqcloud.com/prod/83/app_api.json",
      "https://bj-1337543130.cos.ap-beijing.myqcloud.com/prod/83/app_api.json",
      "https://gz-1337543130.cos.ap-guangzhou.myqcloud.com/prod/83/app_api.json",
      "https://cq-1337543130.cos.ap-chongqing.myqcloud.com/prod/83/app_api.json",
      "https://xg-1337543130.cos.ap-hongkong.myqcloud.com/prod/83/app_api.json",
    ],
  );
}
