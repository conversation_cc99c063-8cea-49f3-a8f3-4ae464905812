import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/account/widgets/account_market_table.dart';
import 'package:gp_stock_app/features/account/widgets/build_action_buttons.dart';
import 'package:gp_stock_app/features/account_v2/contract/screens/account_contract_detail/contract_info_v2/contract_info_screen.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';

import '../../../shared/app/extension/helper.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/routes/app_router.dart';
import '../../account/domain/models/account_summary/contract_summary_response.dart';
import '../../account/logic/account/account_cubit.dart';
import '../../account/widgets/assets_card.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

/// 合约总详情
class ContractDetailScreen extends StatefulWidget {
  final ContractSummaryData contractSummary;
  const ContractDetailScreen({super.key, required this.contractSummary});

  @override
  State<ContractDetailScreen> createState() => _ContractDetailScreenState();
}

class _ContractDetailScreenState extends State<ContractDetailScreen> {
  @override
  void initState() {
    super.initState();
    Helper.afterInit(_init);
  }

  Future<void> _init() async {
    final accountCubit = context.read<AccountCubit>();
    accountCubit.startContractDataPolling(widget.contractSummary.id ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (didPop, result) => context.read<AccountCubit>().stopContractDataPolling(),
      child: Scaffold(
        appBar: AppBar(
          surfaceTintColor: Colors.transparent,
          backgroundColor: context.theme.cardColor,
          title: Text(getContractLabel(widget.contractSummary)),
        ),
        body: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(20.gr),
                    bottomRight: Radius.circular(20.gr),
                  ),
                ),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.gw),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Assets Card
                      BlocBuilder<AccountCubit, AccountState>(
                        builder: (context, state) {
                          return AssetsCard(
                            totalAssets: state.currentContractSummary?.allAsset ?? widget.contractSummary.allAsset,
                            todayEarnings: state.currentContractSummary?.todayWinAmount ??
                                widget.contractSummary.todayWinAmount ??
                                0,
                            availableBalance:
                                state.currentContractSummary?.useAmount ?? widget.contractSummary.useAmount ?? 0,
                            myInterest: state.currentContractSummary?.interestAmount ??
                                widget.contractSummary.interestAmount ??
                                0,
                            frozenAmount:
                                state.currentContractSummary?.freezePower ?? widget.contractSummary.freezePower ?? 0,
                            isContract: true,
                            currency: widget.contractSummary.currency ?? 'CNY',
                            profitTitle: 'today_earnings'.tr(),
                          );
                        },
                      ),
                      14.verticalSpace,
                      // Action Buttons
                      if (widget.contractSummary.type == 1) ...[
                        _ActionButtons1(contractSummary: widget.contractSummary),
                      ],
                      if (widget.contractSummary.type == 2) ...[
                        _ActionButtons2(contractSummary: widget.contractSummary),
                      ],
                      if (widget.contractSummary.type == 3) ...[
                        _ActionButtons3(contractSummary: widget.contractSummary),
                      ],
                      18.verticalSpace,
                    ],
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 16.gw),
                child: Column(
                  children: [
                    14.verticalSpace,
                    AccountMarketTable(contract: widget.contractSummary),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

//standard
//experience
class _ActionButtons1 extends StatelessWidget {
  final ContractSummaryData contractSummary;
  const _ActionButtons1({required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 15.gh,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'expandMargin'.tr(),
                icon: Assets.expandMarginIcon,
                onTap: () => getIt<NavigatorService>().push(AppRouter.routeMarginCall, arguments: {
                  'contractId': contractSummary.id,
                  'contractActionType': ContractAction.marginExpand,
                  'contractType': contractSummary.contractType
                }).then(
                  (value) {
                    if (!context.mounted) return;
                    context.read<AccountCubit>().getCurrentContractSummary(contractSummary.id!);
                  },
                ),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'supplementLoss'.tr(),
                icon: Assets.supplementContractIcon,
                onTap: () => getIt<NavigatorService>().push(AppRouter.routeMarginCall, arguments: {
                  'contractId': contractSummary.id,
                  'contractActionType': ContractAction.replenish,
                  'contractType': contractSummary.contractType
                }),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'cashOut1'.tr(),
                icon: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => Assets.withdrawIcon,
                  _ => Assets.withdrawContractIcon,
                },
                onTap: () {
                  getIt<NavigatorService>().push(AppRouter.routeContractWithdraw, arguments: contractSummary.id);
                },
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'tradingCenter'.tr(),
                icon: Assets.tradingIcon,
                onTap: () {
                  final instrument = getTradingArguments(contractSummary.marketType!);
                  getIt<NavigatorService>().push(AppRouter.routeTradingCenter,
                    arguments: TradingArguments(
                      instrumentInfo: instrument,
                      selectedIndex: TradeTabType.Trading.index,
                      contract: context.read<AccountCubit>().state.currentContractSummary ?? contractSummary,
                      isFromContractDetails: true,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'fundRecords'.tr(),
                icon: Assets.recordsIcon,
                onTap: () => getIt<NavigatorService>().push(AppRouter.routeFundRecords,
                    arguments: {'contractId': contractSummary.id, 'isContractAccount': true}),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'terminateContract'.tr(),
                icon: Assets.terminateContractIcon,
                onTap: () => getIt<NavigatorService>().push(AppRouter.routeTerminateContract, arguments: contractSummary),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'transactionHistory'.tr(),
                icon: Assets.historyIcon,
                onTap: () =>
                    getIt<NavigatorService>().push(AppRouter.routeSpotAndContractHistory, arguments: {'contract': contractSummary}),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'contractDetails'.tr(),
                icon: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => Assets.contractDetailIcon,
                  _ => Assets.withdrawIcon,
                },
                onTap: () => AuthUtils.verifyAuth(
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ContractInfoScreen(
                        contractSummary: contractSummary,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            // Expanded(
            //   child: SizedBox(),
            // ),
          ],
        ),
      ],
    );
  }
}

//bonus
class _ActionButtons3 extends StatelessWidget {
  final ContractSummaryData contractSummary;
  const _ActionButtons3({required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 15.gh,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'expandMargin'.tr(),
                icon: Assets.expandMarginIcon,
                onTap: () {
                  getIt<NavigatorService>().push(AppRouter.routeMarginCall, arguments: {
                    'contractId': contractSummary.id,
                    'contractActionType': ContractAction.marginExpand,
                    'contractType': contractSummary.contractType
                  });
                },
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'supplementLoss'.tr(),
                icon: Assets.supplementContractIcon,
                onTap: () => getIt<NavigatorService>().push(AppRouter.routeMarginCall, arguments: {
                  'contractId': contractSummary.id,
                  'contractActionType': ContractAction.replenish,
                  'contractType': contractSummary.contractType
                }),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'cashOut1'.tr(),
                icon: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => Assets.withdrawIcon,
                  _ => Assets.withdrawContractIcon,
                },
                onTap: () => getIt<NavigatorService>().push(AppRouter.routeContractWithdraw, arguments: contractSummary.id),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'tradingCenter'.tr(),
                icon: Assets.tradingIcon,
                onTap: () {
                  final instrument = getTradingArguments(contractSummary.marketType!);
                  getIt<NavigatorService>().push(AppRouter.routeTradingCenter,
                    arguments: TradingArguments(
                      instrumentInfo: instrument,
                      selectedIndex: TradeTabType.Trading.index,
                      contract: context.read<AccountCubit>().state.currentContractSummary ?? contractSummary,
                      isFromContractDetails: true,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Expanded(
              child: BuildActionButton(
                label: 'funding_records'.tr(),
                icon: Assets.recordsIcon,
                onTap: () => getIt<NavigatorService>().push(AppRouter.routeFundRecords,
                    arguments: {'contractId': contractSummary.id, 'isContractAccount': true}),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'terminateContract'.tr(),
                icon: Assets.terminateContractIcon,
                onTap: () => getIt<NavigatorService>().push(AppRouter.routeTerminateContractV2, arguments: contractSummary),
              ),
            ),

            Expanded(
              child: BuildActionButton(
                label: 'historicalMessages'.tr(),
                icon: Assets.historyIcon,
                onTap: () =>
                    getIt<NavigatorService>().push(AppRouter.routeSpotAndContractHistory, arguments: {'contract': contractSummary}),
              ),
            ),
            Expanded(
              child: BuildActionButton(
                label: 'contractDetails'.tr(),
                icon: switch (AppConfig.instance.skinStyle) {
                  AppSkinStyle.kGP => Assets.contractDetailIcon,
                  _ => Assets.withdrawIcon,
                },
                onTap: () => AuthUtils.verifyAuth(
                  () => Navigator.push(context,
                      MaterialPageRoute(builder: (context) => ContractInfoScreen(contractSummary: contractSummary))),
                ),
              ),
            ),
            // Expanded(
            //   child: SizedBox(),
            // ),
          ],
        ),
      ],
    );
  }
}

// ignore: unused_element
class _ActionButtons2 extends StatelessWidget {
  final ContractSummaryData contractSummary;
  const _ActionButtons2({required this.contractSummary});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Expanded(
          child: BuildActionButton(
            label: 'tradingCenter'.tr(),
            icon: Assets.tradingIcon,
            onTap: () {
              final instrument = getTradingArguments(contractSummary.marketType!);
              getIt<NavigatorService>().push(AppRouter.routeTradingCenter,
                arguments: TradingArguments(
                  instrumentInfo: instrument,
                  selectedIndex: TradeTabType.Trading.index,
                  contract: context.read<AccountCubit>().state.currentContractSummary ?? contractSummary,
                  isFromContractDetails: true,
                ),
              );
            },
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'apply_records'.tr(),
            icon: Assets.recordsIcon,
            onTap: () => getIt<NavigatorService>().push(AppRouter.routeFundRecords,
                arguments: {'contractId': contractSummary.id, 'isContractAccount': true}),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'terminateContract'.tr(),
            icon: Assets.terminateContractIcon,
            onTap: () => getIt<NavigatorService>().push(AppRouter.routeTerminateContract, arguments: contractSummary),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'historicalMessages'.tr(),
            icon: Assets.historyIcon,
            onTap: () =>
                getIt<NavigatorService>().push(AppRouter.routeSpotAndContractHistory, arguments: {'contract': contractSummary}),
          ),
        ),
        Expanded(
          child: BuildActionButton(
            label: 'contractDetails'.tr(),
            icon: switch (AppConfig.instance.skinStyle) {
              AppSkinStyle.kGP => Assets.contractDetailIcon,
              _ => Assets.withdrawIcon,
            },
            onTap: () => AuthUtils.verifyAuth(
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ContractInfoScreen(
                    contractSummary: contractSummary,
                  ),
                ),
              ),
            ),
          ),
        ),
        // Expanded(
        //   child: SizedBox(),
        // ),
      ],
    );
  }
}
