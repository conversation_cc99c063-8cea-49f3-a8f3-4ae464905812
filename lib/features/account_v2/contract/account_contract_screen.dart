import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/build_action_buttons.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account_v2/contract/widgets/contract_asset_section.dart';
import 'package:gp_stock_app/features/account_v2/contract/widgets/contract_summary_cell.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../0_home/account_screen_cubit_v2.dart';
import '../0_home/account_screen_state_v2.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

/// 合约账户
class AccountContractScreen extends StatefulWidget {
  const AccountContractScreen({super.key});

  @override
  State<StatefulWidget> createState() => _AccountContractScreenState();
}

class _AccountContractScreenState extends State<AccountContractScreen> {
  final RefreshController _refreshController = RefreshController();

  @override
  void initState() {
    super.initState();
    // 初始化时请求第一次数据
    context.read<AccountScreenCubitV2>().fetchContractSummaryPage();
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    await context.read<AccountScreenCubitV2>().fetchContractSummaryPage();
    _refreshController
      ..resetNoData()
      ..refreshCompleted();
  }

  void _onLoadMore() async {
    final cubit = context.read<AccountScreenCubitV2>();

    /// 过滤首页为空时加载更多
    if (cubit.state.contractSummaryList.isEmpty) return;
    await cubit.fetchContractSummaryPage(isLoadMore: true);
    if (cubit.state.contractSummaryHaveMoreData) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(height: 12.gw),
        BlocSelector<UserCubit, UserState, double?>(
            selector: (state) => state.accountInfo?.assetAmount ?? 0,
            builder: (context, state) {
              return ContractAssetSection(amount: state);
            }),
        SizedBox(height: 10.gw),
        _buildOperateSection(),
        SizedBox(height: 10.gw),
        Expanded(
            child: BlocSelector<AccountScreenCubitV2, AccountScreenStateV2,
                ({List<ContractSummaryPageRecord> data, bool haveMoreData, DataStatus status})>(
          selector: (state) => (
            data: state.contractSummaryList,
            haveMoreData: state.contractSummaryHaveMoreData,
            status: state.contractSummaryStatus
          ),
          builder: (context, state) {
            final isFetchNewData = state.status == DataStatus.loading && state.data.isEmpty;

            /// 骨架图
            if (isFetchNewData) {
              return _buildShimmerWidget();
            }

            /// 空数据
            if (state.data.isEmpty) {
              return TableEmptyWidget(
                height: 40,
                width: 40,
                title: "no_contracts".tr(),
                backgroundColor: Colors.white,
                margin: EdgeInsets.symmetric(horizontal: 18.gw),
                radius: 10.gw,
              );
            }

            /// 主列表
            return _buildContractListView(state.data);
          },
        )),
        // SizedBox(height: 80.gw),
      ],
    );
  }

  Widget _buildOperateSection() {
    switch (AppConfig.instance.skinStyle) {
      case AppSkinStyle.kGP:
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            BuildActionButton(
              label: 'applyContract2'.tr(),
              icon: Assets.contractIcon1,
              onTap: () {
                AuthUtils.verifyAuth(() async {
                  await getIt<NavigatorService>().push(AppRouter.routeContractApply,
                    arguments: {'mainContractType': InstrumentType.stock},
                  );
                  if (mounted) {
                    context.read<AccountScreenCubitV2>().fetchContractSummaryPage();
                  }
                });
              },
            ),
            BuildActionButton(
              label: 'apply_records'.tr(),
              icon: Assets.contractIcon2,
              onTap: () {
                context.verifyRealName(() async {
                  await getIt<NavigatorService>().push(AppRouter.routeContractApplyRecord);
                  if (mounted) {
                    context.read<AccountScreenCubitV2>().fetchContractSummaryPage();
                  }
                });
              },
            ),
            BuildActionButton(
              label: 'historicalMessages'.tr(),
              icon: Assets.contractIcon3,
              onTap: () => context.verifyRealName(() => getIt<NavigatorService>().push(AppRouter.routeContractSettleHistory)),
            ),
          ],
        );

      case AppSkinStyle.kTemplateA || AppSkinStyle.kTemplateB || AppSkinStyle.kTemplateC || AppSkinStyle.kTemplateD:
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BuildActionButton(
              label: 'applyContract2'.tr(),
              icon: Assets.contractIcon,
              onTap: () {
                AuthUtils.verifyAuth(() async {
                  await getIt<NavigatorService>().push(AppRouter.routeContractApply,
                    arguments: {'mainContractType': InstrumentType.stock},
                  );
                  if (mounted) {
                    context.read<AccountScreenCubitV2>().fetchContractSummaryPage();
                  }
                });
              },
            ),
            BuildActionButton(
              label: 'apply_records'.tr(),
              icon: Assets.recordsIcon,
              onTap: () => getIt<NavigatorService>().push(AppRouter.routeContractApplyRecord),
            ),
            BuildActionButton(
              label: 'historicalMessages'.tr(),
              icon: Assets.historyIcon,
              onTap: () => getIt<NavigatorService>().push(AppRouter.routeContractSettleHistory),
            ),
          ],
        );
    }
  }

  Widget _buildShimmerWidget() {
    // 生成 3 个条目索引
    return Column(
      children: List.generate(3, (_) => ContractSummaryShimmerCell()),
    );
  }

  Widget _buildContractListView(List<ContractSummaryPageRecord> data) {
    return CommonRefresher(
      controller: _refreshController,
      enablePullDown: true,
      enablePullUp: true,
      onRefresh: () => _onRefresh(),
      onLoading: () => _onLoadMore(),
      child: Builder(
        builder: (context) {
          return AnimationLimiter(
            child: ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: data.length,
              padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 10.gh),
              separatorBuilder: (_, __) => 10.verticalSpace,
              itemBuilder: (context, index) {
                return AnimationConfiguration.staggeredList(
                  position: index,
                  duration: const Duration(milliseconds: 600),
                  child: SlideAnimation(
                    verticalOffset: 30.0,
                    child: FadeInAnimation(
                      child: ContractSummaryCell(
                        model: data[index],
                        onTap: () async {
                          await getIt<NavigatorService>().push(AppRouter.routeContractDetail,
                            arguments: {'model': data[index]},
                          );

                          if (context.mounted) {
                            context.read<AccountScreenCubitV2>().fetchContractSummaryPage();
                          }
                        },
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        },
      ),
    );
  }
}
