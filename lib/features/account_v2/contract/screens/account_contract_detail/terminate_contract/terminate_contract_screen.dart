import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/contract/contract.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/convert_helper.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/functions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/alert_dilaog/common_dialog.dart';
import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';

import 'terminate_contract_cubit.dart';
import 'terminate_contract_state.dart';

/// 终止合约
class TerminateContractPage extends StatelessWidget {
  const TerminateContractPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: context.theme.cardColor,
        title: Text('terminateContract'.tr()),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.gr),
        child: BlocBuilder<TerminateContractCubit, TerminateContractState>(
          builder: (context, state) {
            return Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        _buildContractInfoSection(context: context, contractSummary: state.model),
                        16.verticalSpace,
                        _buildAmountSection(context: context, contractSummary: state.model),
                      ],
                    ),
                  ),
                ),
                24.verticalSpace,
                _buildTerminateButton(context: context, contractSummary: state.model),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildContractInfoSection(
      {required BuildContext context, required ContractSummaryPageRecord contractSummary}) {
    String formatDateRange() {
      try {
        return '${ConvertHelper.formatDateGeneral(contractSummary.openTime)} - ${ConvertHelper.formatDateGeneral(contractSummary.expireTime)}';
      } catch (e) {
        return ConvertHelper.formatDateGeneral(contractSummary.openTime);
      }
    }

    return ShadowBox(
      child: Column(
        spacing: 12.gh,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AmountRow(
            title: 'contractType'.tr(),
            value: getContractName(
              marketType: contractSummary.marketType,
              type: contractSummary.type,
              periodType: contractSummary.periodType,
              multiple: contractSummary.multiple,
              id: contractSummary.id,
            ),
          ),
          AmountRow(
            title: 'period'.tr(),
            value: getPeriodType(contractSummary.periodType),
          ),
          AmountRow(
            title: 'date'.tr(),
            value: formatDateRange(),
          ),
          AmountRow(
            title: 'contractMultiple'.tr(),
            value: '${contractSummary.multiple}${'times'.tr()}',
          ),
        ],
      ),
    );
  }

// Helper function to get period type text
  String getPeriodType(int? period) => switch (period) {
        1 => 'daily'.tr(),
        2 => 'weekly'.tr(),
        3 => 'monthly'.tr(),
        _ => 'daily'.tr(),
      };

  Widget _buildAmountSection({required BuildContext context, required ContractSummaryPageRecord contractSummary}) {
    return ShadowBox(
      child: Column(
        spacing: 12.gh,
        children: [
          AmountRow(
            title: 'totalMargin'.tr(),
            amount: contractSummary.totalPower,
          ),
          AmountRow(
            title: 'initialMargin'.tr(),
            amount: contractSummary.initCash,
          ),
          AmountRow(
            title: 'warningLine'.tr(),
            amount: contractSummary.warnRemindAmount,
          ),
          AmountRow(
            title: 'stopLossLine'.tr(),
            amount: contractSummary.closeRemindAmount,
          ),
          AmountRow(
            title: 'interestAmount'.tr(),
            amount: contractSummary.interestAmount,
          ),
          AmountRow(
            title: 'marketValue'.tr(),
            amount: contractSummary.positionAmount,
          ),
          AmountRow(
            title: 'expandedMargin'.tr(),
            amount: contractSummary.expendAmount,
          ),
          AmountRow(
            title: 'supplementLoss'.tr(),
            amount: contractSummary.coverLossAmount,
          ),
          AmountRow(
            title: 'floatingProfitLoss'.tr(),
            amount: contractSummary.winAmount,
          ),
          AmountRow(
            title: 'withdrawAmount'.tr(),
            amount: contractSummary.withdrawAmount,
          ),
          AmountRow(
            title: 'frozenAmount'.tr(),
            amount: contractSummary.freezePower,
          ),
          AmountRow(
            title: 'contractNetAssets'.tr(),
            amount: contractSummary.contractAssetAmount,
          ),
          AmountRow(
            title: 'distanceToWarningLine'.tr(),
            amount: contractSummary.gapWarnRemindAmount,
          ),
          AmountRow(
            title: 'distanceToLiquidationLine'.tr(),
            amount: contractSummary.gapCloseRemindAmount,
          ),
          AmountRow(
            title: 'contractRenewalStatus'.tr(),
            value: contractSummary.isAutoRenew ? 'autoRenewal'.tr() : 'settleOnExpiration'.tr(),
          ),
        ],
      ),
    );
  }

  Widget _buildTerminateButton({required BuildContext context, required ContractSummaryPageRecord contractSummary}) {
    return BlocConsumer<TerminateContractCubit, TerminateContractState>(
      listenWhen: (previous, current) => previous.netStatus != current.netStatus,
      listener: (context, state) {
        if (state.netStatus == DataStatus.success) {
          context.read<AccountCubit>().getContractSummary();
          getIt<NavigatorService>().pop();
          // Navigator.popUntil(navigatorKey.currentContext!, (route) => route.isFirst);
        }
      },
      builder: (context, state) {
        return SizedBox(
          width: double.infinity,
          child: CommonButton(
            onPressed: () {
              CommonDialog(
                context,
                title: "confirm_terminate_contract".tr(),
                complete: () {
                  context.read<TerminateContractCubit>().terminateContract(contractId: contractSummary.id.toString());
                },
              ).show();
            },
            title: 'terminate'.tr(),
            style: CommonButtonStyle.stockRed,
            showLoading: state.netStatus.isLoading,
          ),
        );
      },
    );
  }
}
