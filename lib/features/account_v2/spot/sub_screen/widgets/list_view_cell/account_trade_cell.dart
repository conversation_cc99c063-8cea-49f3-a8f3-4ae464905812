import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

import 'package:gp_stock_app/core/utils/convert_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/shadow_box.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';
import 'package:shimmer/shimmer.dart';

/// 成交明细Cell
class AccountTradeCell extends StatelessWidget {
  const AccountTradeCell({
    super.key,
    required this.data,
    required this.onTap,
    this.isLast = false,
  });

  final FTradeAcctOrderRecords data;
  final VoidCallback onTap;
  final bool isLast;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: ShadowBox(

        borderRadius: isLast
            ? BorderRadius.only(
                bottomLeft: Radius.circular(10.gr),
                bottomRight: Radius.circular(10.gr),
              )
            : BorderRadius.zero,
        padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 8.gw),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Name and Symbol Column
            Expanded(
              flex: 6,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    data.symbolName,
                    style: switch (AppConfig.instance.skinStyle) {
                      AppSkinStyle.kTemplateD => context.textTheme.title.w600,
                    _ => context.textTheme.regular.w600,
                    },
                  ),
                  Row(
                    spacing: 5.gh,
                    children: [
                      SymbolChip(name: data.market, chipColor: context.theme.primaryColor),
                      Text(
                        data.symbol,
                        style: context.textTheme.regular.fs12,
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Latest Price Column
            Expanded(
              flex: 5,
              child: Column(
                children: [
                  Text(
                    data.tradePrice.toStringAsFixed(2),
                    textAlign: TextAlign.center,
                    style: context.textTheme.primary.ffAkz,
                  ),
                  Text(
                    data.tradeNum.toStringAsFixed(1),
                    textAlign: TextAlign.center,
                    style: context.textTheme.regular.fs12,
                  ),
                ],
              ),
            ),
            Expanded(
                flex: 5,
                child: AnimatedFlipCounter(
                  fractionDigits: 2,
                  decimalSeparator: '.',
                  thousandSeparator: ',',
                  textStyle: context.textTheme.primary.w700.ffAkz,
                  value: data.transactionAmount,
                )),
            // Change Percentage Column
            Expanded(
              flex: 5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    positionActionStr(),
                    textAlign: TextAlign.center,
                    style: context.textTheme.regular.fs12.copyWith(
                        color:
                            TradeDirection.getColor(context, tradeDirection: TradeDirection.fromValue(data.direction))),
                  ),
                  Text(
                    ConvertHelper.formatDateTypeIn24Hour(data.dealTime),
                    textAlign: TextAlign.center,
                    style: context.textTheme.regular.fs8.w600,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String positionActionStr() {
    if (data.direction == 1 && data.tradeType == 1) {
      return "openLong".tr();
    }
    if (data.direction == 1 && data.tradeType == 2) {
      return "openShort".tr();
    }
    if (data.direction == 2 && data.tradeType == 1) {
      return "sellLong".tr();
    }
    if (data.direction == 2 && data.tradeType == 2) {
      return "sellShort".tr();
    }
    return "--";
  }
}

class AccountTradeShimmerCell extends StatelessWidget {
  const AccountTradeShimmerCell({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 18.gw),
      padding: EdgeInsets.symmetric(horizontal: 6.gw, vertical: 8.gw),
      color: Colors.white,
      child: Shimmer.fromColors(
        baseColor: context.theme.dividerColor,
        highlightColor: context.theme.cardColor.withValues(alpha: 0.5),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 左侧：名称 + 市场 + 代码
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _box(60, 10),
                const SizedBox(height: 1),
                _box(36, 10),
              ],
            ),

            // 成交价 + 成交量
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _box(50, 10),
                const SizedBox(height: 1),
                _box(40, 10),
              ],
            ),

            // 成交金额
            Align(
              alignment: Alignment.center,
              child: _box(60, 10),
            ),

            // 买卖方向 + 时间
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                _box(60, 10),
                const SizedBox(height: 1),
                _box(50, 10),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _box(double width, double height, {double borderRadius = 2}) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey.shade300,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}
