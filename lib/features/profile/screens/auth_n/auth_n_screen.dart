import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/profile/widgets/auth_n/auth_n_shimmer.dart';

import '../../../../core/api/network/network_helper.dart';
import '../../../../shared/app/extension/helper.dart';
import '../../../../shared/app/utilities/easy_loading.dart';
import '../../../../shared/constants/enums.dart';
import '../../../../shared/mixin/animation.dart';

import '../../../../shared/widgets/buttons/common_button.dart';
import '../../domain/models/auth_n/info/auth_n_info.dart';
import '../../logic/auth_n/auth_n_cubit.dart';
import '../../widgets/auth_n/auth_form.dart';
import '../../widgets/auth_n/auth_header.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';

class AuthNScreen extends StatefulWidget {
  const AuthNScreen({super.key});

  @override
  State<AuthNScreen> createState() => _AuthNScreenState();
}

class _AuthNScreenState extends State<AuthNScreen> {
  @override
  void initState() {
    super.initState();
    Helper.afterInit(() => context.read<AuthNCubit>().getAuthNInfo());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: BlocConsumer<AuthNCubit, AuthNState>(
        listenWhen: (previous, current) => (previous.getAuthNInfoStatus != current.getAuthNInfoStatus &&
            (current.getAuthNInfoStatus == DataStatus.failed || current.getAuthNInfoStatus == DataStatus.success)),
        listener: (context, state) {
          if (state.getAuthNInfoStatus == DataStatus.failed) {
            GPEasyLoading.showToast(state.error ?? 'somethingWentWrong'.tr());
          } else if (state.getAuthNInfoStatus == DataStatus.success && state.isRefreshing) {
            GPEasyLoading.showToast('refreshSuccess'.tr());
          }
        },
        buildWhen: (previous, current) => previous.getAuthNInfoStatus != current.getAuthNInfoStatus,
        builder: (context, state) {
          return RefreshIndicator(
            color: context.theme.primaryColor,
            backgroundColor: context.theme.cardColor,
            strokeWidth: 2.0,
            onRefresh: () async {
              await context.read<AuthNCubit>().getAuthNInfo(shouldRefresh: true);
            },
            child: BlocSelector<AuthNCubit, AuthNState, DataStatus>(
              selector: (state) => state.getAuthNInfoStatus,
              builder: (context, status) {
                return ListView(
                  physics: const AlwaysScrollableScrollPhysics(parent: BouncingScrollPhysics()),
                  shrinkWrap: true,
                  children: [
                    AnimationLimiter(
                      child: Stack(
                        children: [
                          const AuthHeader(),
                          if (status == DataStatus.success)
                            BlocSelector<AuthNCubit, AuthNState, AuthNInfo?>(
                              selector: (state) => state.authNInfo,
                              builder: (context, authNInfo) =>
                                  _AuthBody(authNInfo: authNInfo, hideDocumentVerification: true),
                            )
                          else
                            const AuthNShimmer(),
                        ],
                      ),
                    ),
                    // Add extra space at the bottom to ensure pull-to-refresh works properly
                    SizedBox(height: 50.gh),
                  ],
                );
              },
            ),
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(50),
      child: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.light,
        elevation: 0,
        backgroundColor: context.theme.primaryColor,
        leading: BackButton(color: Colors.white),
      ),
    );
  }
}

class _AuthBody extends StatefulWidget {
  final AuthNInfo? authNInfo;
  final bool hideDocumentVerification;

  const _AuthBody({required this.authNInfo, this.hideDocumentVerification = true});

  @override
  State<_AuthBody> createState() => _AuthBodyState();
}

class _AuthBodyState extends State<_AuthBody> with StaggeredAnimation {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _idController = TextEditingController();
  final TextEditingController _withdrawPasswordController = TextEditingController();
  final TextEditingController _bankReservedMobileController = TextEditingController();
  final TextEditingController _bankCardNumberController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    if (widget.authNInfo != null) {
      _nameController.text = widget.authNInfo!.realName ?? '';
      _idController.text = widget.authNInfo!.idCard ?? '';
      _nameController.selection = TextSelection.collapsed(offset: _nameController.text.length);
      _idController.selection = TextSelection.collapsed(offset: _idController.text.length);
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _idController.dispose();
    _withdrawPasswordController.dispose();
    _bankReservedMobileController.dispose();
    _bankCardNumberController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 50.gh, left: 12.gw, right: 12.gw),
      child: Form(
        key: _formKey,
        child: Column(children: staggeredAnimationScale(children: [_buildFormCard(), _buildSubmitButton()])),
      ),
    );
  }

  Widget _buildFormCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        boxShadow: [BoxShadow(color: Colors.black.withNewOpacity(0.1), blurRadius: 10)],
      ),
      child: Padding(
        padding: EdgeInsets.all(16.gr),
        child: AuthFormFields(
          cardNameController: _nameController,
          cardNumberController: _idController,
          withdrawPasswordController: _withdrawPasswordController,
          authNInfo: widget.authNInfo,
          hideDocumentVerification: widget.hideDocumentVerification,
          bankReservedMobileController: _bankReservedMobileController,
          bankCardNumberController: _bankCardNumberController,
        ),
      ),
    );
  }

  Widget _buildSubmitButton() {
    final bool showButton = widget.authNInfo?.status == 2 || // Rejected
        (widget.authNInfo?.idCard?.isEmpty ?? true); // New submission

    if (!showButton) return const SizedBox.shrink();

    return Padding(
      padding: EdgeInsets.only(top: 24.gh),
      child: MultiBlocListener(
        listeners: [
          BlocListener<AuthNCubit, AuthNState>(
            listenWhen: (previous, current) =>
                previous.submitCardInformationStatus != current.submitCardInformationStatus,
            listener: _handleSubmitStatusChange,
          ),
          if (!widget.hideDocumentVerification)
            BlocListener<AuthNCubit, AuthNState>(
              listenWhen: (previous, current) => previous.uploadStatus != current.uploadStatus,
              listener: _handleUploadStatusChange,
            ),
        ],
        child: BlocBuilder<AuthNCubit, AuthNState>(
          buildWhen: (previous, current) =>
              previous.submitCardInformationStatus != current.submitCardInformationStatus ||
              (!widget.hideDocumentVerification && previous.uploadStatus != current.uploadStatus),
          builder: (context, state) {
            final bool isLoading = state.submitCardInformationStatus == DataStatus.loading ||
                (!widget.hideDocumentVerification && state.uploadStatus == DataStatus.loading);

            return CommonButton(
              showLoading: isLoading,
              onPressed: () => _submitForm(context),
              title: 'submit'.tr(),
            );
          },
        ),
      ),
    );
  }

  void _submitForm(BuildContext context) {
    if (_formKey.currentState!.validate()) {
      // Bank card number and reserved mobile fields are temporarily not required
      // Using empty strings for these fields as they're currently hidden in the UI
      context.read<AuthNCubit>().submitCardInformation(
            cardNumber: _idController.text.trim(),
            cardName: _nameController.text.trim(),
            withdrawPassword: _withdrawPasswordController.text.trim(),
            bankMobile: '', // Temporarily not required
            bankCardNumber: '', // Temporarily not required
          );
    }
  }

  void _handleSubmitStatusChange(BuildContext context, AuthNState state) async {
    if (state.submitCardInformationStatus == DataStatus.failed) {
      NetworkHelper.handleMessage(
        state.error,
        type: HandleTypes.customDialog,
        snackBarType: SnackBarType.error,
        dialogKey: 'auth_n_submit_error_dialog',
      );
    } else if (state.submitCardInformationStatus == DataStatus.success) {
      NetworkHelper.handleMessage(
        'verificationSubmittedSuccessfully'.tr(),
        type: HandleTypes.snackbar,
        snackBarType: SnackBarType.success,
      );
      if (!widget.hideDocumentVerification) {
        context.read<AuthNCubit>().resetFile();
      }
      await context.read<AuthNCubit>().getAuthNInfo();
    }
  }

  void _handleUploadStatusChange(BuildContext context, AuthNState state) {
    if (state.uploadStatus == DataStatus.failed) {
      NetworkHelper.handleMessage(
        state.error,
        type: HandleTypes.customDialog,
        snackBarType: SnackBarType.error,
        dialogKey: 'auth_n_upload_error_dialog',
      );
    }
  }
}
