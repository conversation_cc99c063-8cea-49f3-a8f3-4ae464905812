import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/validators.dart';
import 'package:gp_stock_app/features/profile/logic/profile/profile_cubit.dart';
import 'package:gp_stock_app/shared/app/extension/helper.dart';
import 'package:gp_stock_app/shared/constants/assets.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

import 'package:gp_stock_app/shared/widgets/buttons/common_button.dart';
import 'package:gp_stock_app/shared/widgets/custom_pin_keyboard.dart';
import 'package:gp_stock_app/shared/widgets/text_fields/text_field_widget.dart';
import 'package:pinput/pinput.dart';

import '../../../../shared/app/utilities/easy_loading.dart';
import '../../../main/widgets/draggable_float_widget.dart';
import '../../widgets/settings/settings_appbar.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ChangePasswordScreen extends StatefulWidget {
  final PasswordModifyType type;
  const ChangePasswordScreen({super.key, required this.type});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final TextEditingController _originalPasswordController = TextEditingController();
  final TextEditingController _newPasswordController = TextEditingController();
  final TextEditingController _confirmPasswordController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  final ValueNotifier<bool> _isFormValid = ValueNotifier<bool>(false);

  late PinTheme defaultPinTheme;

  @override
  void initState() {
    super.initState();
    for (var controller in [_originalPasswordController, _newPasswordController, _confirmPasswordController]) {
      controller.addListener(_validateForm);
    }
  }

  @override
  void dispose() {
    _originalPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _isFormValid.dispose();
    super.dispose();
  }

  void _validateForm() {
    if (widget.type == PasswordModifyType.financial) {
      final isOriginalPasswordValid = _originalPasswordController.text.length == 6;
      final isNewPasswordValid = _newPasswordController.text.length == 6;
      _isFormValid.value = isOriginalPasswordValid && isNewPasswordValid;
      return;
    }

    final isOriginalPasswordValid = _originalPasswordController.text.isNotEmpty;
    final isNewPasswordValid = Validators.validatePassword(_newPasswordController.text) == null;
    final isConfirmPasswordValid = _confirmPasswordController.text.isNotEmpty &&
        _confirmPasswordController.text == _newPasswordController.text &&
        isNewPasswordValid;

    _isFormValid.value = isOriginalPasswordValid && isNewPasswordValid && isConfirmPasswordValid;
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate() && !_isLoading) {
      setState(() => _isLoading = true);

      final result = await context.read<ProfileCubit>().changePassword(
            password: _newPasswordController.text,
            originalPassword: _originalPasswordController.text,
            type: PasswordChangeType.accountVerification,
            passwordType: widget.type == PasswordModifyType.account ? PasswordType.account : PasswordType.withdrawal,
          );

      setState(() => _isLoading = false);

      if (result && mounted) {
        GPEasyLoading.showToast('passwordChangedSuccessfully'.tr());
        if (widget.type == PasswordModifyType.account) {
          Helper.logoutUser();
        } else {
          Navigator.pop(context);
        }
      } else if (mounted) {
        final error = context.read<ProfileCubit>().state.error ?? 'failedToChangePassword'.tr();
        final errorColor = context.colorTheme.stockRed;
        GPEasyLoading.showToast(error, bgColor: errorColor);
      }
    }
  }

  void _showKeyboard(TextEditingController controller) {
    // Store original position
    final oldPosition = FloatingPosition.bottomRight;

    // Move to top right before showing keyboard
    FloatingWidgetManager().updatePosition(FloatingPosition.centerRight);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.transparent,
      builder: (_) => WithdrawalPasswordKeyboard(
        controller: controller,
        bottomPadding: MediaQuery.of(context).viewInsets.bottom + MediaQuery.of(context).padding.bottom,
        onChanged: (_) => _validateForm(),
        onSubmit: () {
          Navigator.pop(context);
          FocusScope.of(context).unfocus();
        },
      ),
    ).whenComplete(() {
      // Move back to original position when sheet is closed in any way
      FloatingWidgetManager().updatePosition(oldPosition);
    });
  }

  @override
  Widget build(BuildContext context) {
    // Initialize the defaultPinTheme here where context is available
    defaultPinTheme = PinTheme(
      width: 48,
      height: 48,
      textStyle: context.textTheme.primary.w500,
      decoration: BoxDecoration(
        color: context.theme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.circular(10.gr),
        border: Border.all(color: context.theme.primaryColor, width: 0.1),
      ),
    );

    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      appBar: SettingsAppBar(
        title: 'changeOriginalPassword'.tr(),
        centreTitle: true,
      ),
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: ListView(
            padding: EdgeInsets.all(16.gw),
            children: [
              if (widget.type != PasswordModifyType.financial)
                Column(
                  children: [
                    _buildPasswordRequirements(),
                    10.verticalSpace,
                  ],
                ),
              _buildFormFields(),
              24.verticalSpace,
              ValueListenableBuilder<bool>(
                valueListenable: _isFormValid,
                builder: (context, isValid, child) {
                  return CommonButton(
                    height: 44.gh,
                    title: 'changePasswordButton'.tr(),
                    onPressed: _isLoading || !isValid ? null : _submit,
                    showLoading: _isLoading,
                    enable: isValid,
                    radius: 8.gr,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPinInput(TextEditingController controller, String label) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: context.textTheme.stockRed.w500,
        ),
        12.verticalSpace,
        GestureDetector(
          onTap: () => _showKeyboard(controller),
          child: AbsorbPointer(
            child: Pinput(
              controller: controller,
              length: 6,
              obscureText: true,
              defaultPinTheme: defaultPinTheme,
              focusedPinTheme: defaultPinTheme.copyWith(
                decoration: defaultPinTheme.decoration!.copyWith(
                  border: Border.all(color: context.theme.primaryColor, width: 0.8),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPasswordRequirements() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.gr),
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        border: Border.all(color: context.theme.dividerColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'passwordRequirements'.tr(),
            style: context.textTheme.primary.w600.copyWith(
              color: context.theme.primaryColor,
            ),
          ),
          8.verticalSpace,
          Text(
            'passwordRequirementsDetails'.tr(),
            style: context.textTheme.regular.fs12,
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields() {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
        border: Border.all(color: context.theme.dividerColor),
      ),
      padding: EdgeInsets.all(16.gr),
      child: widget.type == PasswordModifyType.financial
          ? Column(
              children: [
                _buildPinInput(_originalPasswordController, 'currentPassword'.tr()),
                24.verticalSpace,
                _buildPinInput(_newPasswordController, 'newPassword'.tr()),
              ],
            )
          : Column(
              children: [
                TextFieldWidget(
                  controller: _originalPasswordController,
                  hintText: 'currentPassword'.tr(),
                  textInputType: TextInputType.visiblePassword,
                  prefixIcon: SvgPicture.asset(
                    Assets.lockIcon,
                    fit: BoxFit.scaleDown,
                    width: 18.gw,
                    height: 18.gh,
                  ),
                  obscureText: true,
                  passwordIcon: true,
                  fillColor: Colors.transparent,
                  borderType: TextFieldBorderType.none,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'enterCurrentPassword'.tr();
                    }
                    return null;
                  },
                ),
                Divider(color: context.theme.dividerColor),
                TextFieldWidget(
                  controller: _newPasswordController,
                  hintText: 'newPassword'.tr(),
                  textInputType: TextInputType.visiblePassword,
                  prefixIcon: SvgPicture.asset(
                    Assets.lockIcon,
                    fit: BoxFit.scaleDown,
                    width: 18.gw,
                    height: 18.gh,
                  ),
                  obscureText: true,
                  passwordIcon: true,
                  fillColor: Colors.transparent,
                  borderType: TextFieldBorderType.none,
                  validator: (value) => Validators.validatePassword(value),
                ),
                Divider(color: context.theme.dividerColor),
                TextFieldWidget(
                  controller: _confirmPasswordController,
                  hintText: 'confirmNewPassword'.tr(),
                  textInputType: TextInputType.visiblePassword,
                  prefixIcon: SvgPicture.asset(
                    Assets.lockIcon,
                    fit: BoxFit.scaleDown,
                    width: 18.gw,
                    height: 18.gh,
                  ),
                  obscureText: true,
                  passwordIcon: true,
                  fillColor: Colors.transparent,
                  borderType: TextFieldBorderType.none,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'enterConfirmPassword'.tr();
                    }
                    if (value != _newPasswordController.text) {
                      return 'passwordsDoNotMatch'.tr();
                    }
                    return null;
                  },
                ),
              ],
            ),
    );
  }
}
