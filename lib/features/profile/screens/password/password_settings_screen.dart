import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/widgets/list_tile/list_tile.dart';

import '../../../../shared/constants/enums.dart';
import '../../../../shared/routes/app_router.dart';
import '../../widgets/settings/settings_appbar.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class PasswordSettingsScreen extends StatelessWidget {
  const PasswordSettingsScreen({super.key, required this.type});
  final PasswordModifyType type;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      appBar: SettingsAppBar(title: type.title.tr()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.gw),
        child: Column(
          children: [
            14.verticalSpace,
            CommonListTile(
              borderRadius: 10.gr,
              showBorder: false,
              title: 'phoneVerification'.tr(),
              onTap: () => getIt<NavigatorService>().push(AppRouter.routeChangePasswordWithPhone, arguments: {'type': type}),
            ),
            10.verticalSpace,
            CommonListTile(
              borderRadius: 10.gr,
              showBorder: false,
              title: 'changeOriginalPassword'.tr(),
              onTap: () => getIt<NavigatorService>().push(AppRouter.routeChangePassword, arguments: {'type': type}),
            ),
          ],
        ),
      ),
    );
  }
}
