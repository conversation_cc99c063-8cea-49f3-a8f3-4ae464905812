import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/sort_color/sort_color_cubit.dart';

import '../../../../core/utils/cache_utils.dart';
import '../../../../shared/app/extension/helper.dart';
import '../../../../shared/app/utilities/easy_loading.dart';
import '../../../../shared/logic/theme/theme_cubit.dart';
import '../../../../shared/routes/app_router.dart';

import '../../../../shared/widgets/list_tile/list_tile.dart';
import '../../../../shared/widgets/buttons/common_button.dart';
import '../../widgets/settings/lanaguage_dialog.dart';
import '../../widgets/settings/settings_appbar.dart';
import '../../widgets/settings/stock_order_dialog.dart';
import '../../widgets/settings/theme_dialog.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: context.theme.scaffoldBackgroundColor,
      appBar: SettingsAppBar(title: 'systemSettings'.tr()),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.gw),
        child: Column(
          children: [
            10.verticalSpace,
            // Personal Info Card
            CommonListTile(
              title: 'personalInfo'.tr(),
              onTap: () => getIt<NavigatorService>().push(AppRouter.routePersonalInfoEdit),
              showBorder: false,
            ),
            12.verticalSpace,
            // Password Section
            Column(
              children: [
                CommonListTile(
                  title: 'password_account'.tr(),
                  onTap: () =>
                      getIt<NavigatorService>().push(AppRouter.routePassword, arguments: {'type': PasswordModifyType.account}),
                  borderRadiusGeometry: BorderRadius.only(
                    topLeft: Radius.circular(8.gr),
                    topRight: Radius.circular(8.gr),
                  ),
                ),
                CommonListTile(
                  title: 'password_financial'.tr(),
                  onTap: () =>
                      getIt<NavigatorService>().push(AppRouter.routePassword, arguments: {'type': PasswordModifyType.financial}),
                  showBorder: false,
                  borderRadiusGeometry: BorderRadius.only(
                    bottomLeft: Radius.circular(8.gr),
                    bottomRight: Radius.circular(8.gr),
                  ),
                ),
              ],
            ),
            12.verticalSpace,
            // Preferences Section
            _PreferencesSection(),
            12.verticalSpace,
            // Cache Section
            _CacheSection(),
          ],
        ),
      ),
    );
  }
}

class _PreferencesSection extends StatelessWidget {
  const _PreferencesSection();

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        CommonListTile(
          title: 'language'.tr(),
          value: Helper().getLanguageName(context.locale),
          onTap: () => showModalBottomSheet(
            backgroundColor: context.theme.scaffoldBackgroundColor,
            context: context,
            builder: (context) => LanguageDialog(),
          ),
          borderRadiusGeometry: BorderRadius.only(
            topLeft: Radius.circular(8.gr),
            topRight: Radius.circular(8.gr),
          ),
        ),
        BlocSelector<ThemeCubit, ThemeState, ThemeMode>(
          selector: (state) => state.themeMode,
          builder: (context, themeMode) {
            return CommonListTile(
              title: 'theme'.tr(),
              value: themeMode == ThemeMode.dark ? 'darkTheme'.tr() : 'lightTheme'.tr(),
              onTap: () => showModalBottomSheet(
                context: context,
                builder: (context) => ThemeDialog(),
              ),
              borderRadiusGeometry: BorderRadius.zero,
            );
          },
        ),
        BlocBuilder<SortColorCubit, SortColorState>(
          builder: (context, state) {
            return CommonListTile(
              title: 'priceColor'.tr(),
              showBorder: false,
              value: state.marketColor.tr.tr(),
              onTap: () => showModalBottomSheet(
                context: context,
                builder: (context) => StockOrderDialog(),
              ),
              borderRadiusGeometry: BorderRadius.only(
                bottomLeft: Radius.circular(8.gr),
                bottomRight: Radius.circular(8.gr),
              ),
            );
          },
        ),
      ],
    );
  }
}

class _CacheSection extends StatefulWidget {
  const _CacheSection();

  @override
  State<_CacheSection> createState() => _CacheSectionState();
}

class _CacheSectionState extends State<_CacheSection> {
  String _cacheSize = '0.00';

  @override
  void initState() {
    super.initState();
    _loadCacheSize();
  }

  Future<void> _loadCacheSize() async {
    final size = await getCacheSize();
    if (mounted) {
      setState(() {
        _cacheSize = size.toStringAsFixed(2);
      });
    }
  }

  Future<void> _showClearCacheDialog() async {
    return showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('confirmClearCache'.tr(), style: context.textTheme.active.fs16),
        content: Text(
          'confirmClearCacheMsg'.tr(),
          style: context.textTheme.regular.w500,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.gr),
        ),
        actions: [
          CommonButton(
            style: CommonButtonStyle.outlined,
            width: 80,
            height: 36,
            title: 'cancel'.tr(),
            onPressed: () => Navigator.pop(context),
            textColor: context.colorTheme.buttonSecondary,
            fontSize: 14.gsp,
          ),
          8.horizontalSpace,
          CommonButton(
            width: 80,
            height: 36,
            title: 'confirm'.tr(),
            fontSize: 14.gsp,
            onPressed: () async {
              Navigator.pop(context);

              // Show loading
              GPEasyLoading.showLoading(message: 'clearing'.tr());

              // Clear cache
              await clearAppCache();

              // Show success and refresh size
              GPEasyLoading.showSuccess(message: 'clearSuccess'.tr());
              _loadCacheSize();
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CommonListTile(
      title: 'clearCache'.tr(),
      value: '$_cacheSize KB',
      onTap: _showClearCacheDialog,
      showBorder: false,
    );
  }
}

class _CacheListTile extends StatefulWidget {
  final double borderRadius;
  final Function(Function refreshCache) onTap;

  const _CacheListTile({
    required this.borderRadius,
    required this.onTap,
  });

  @override
  State<_CacheListTile> createState() => _CacheListTileState();
}

class _CacheListTileState extends State<_CacheListTile> {
  String _cacheSize = '0.00';

  @override
  void initState() {
    super.initState();
    _loadCacheSize();
  }

  Future<void> _loadCacheSize() async {
    final size = await getCacheSize();
    if (mounted) {
      setState(() {
        _cacheSize = size.toStringAsFixed(2);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return CommonListTile(
      borderRadius: widget.borderRadius,
      title: 'clearCache'.tr(),
      value: '$_cacheSize KB',
      onTap: () => widget.onTap(_loadCacheSize),
      showBorder: false,
    );
  }
}
