import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/account/domain/models/user_wallet/user_wallet_model.dart';
import 'package:gp_stock_app/features/account/domain/repository/bank_repository.dart';
import 'package:gp_stock_app/features/profile/domain/models/third_party_channel_entity_list_entity.dart';
import 'package:gp_stock_app/features/profile/domain/repository/third_party_channel_repository.dart';
import 'package:gp_stock_app/features/profile/domain/services/third_party_service_v2.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/constants/enums.dart';
import '../../domain/models/third_party_success_response/third_party_success_response.dart';

part 'third_party_channel_state.dart';

@singleton
class ThirdPartyChannelCubit extends Cubit<ThirdPartyChannelState> {
  ThirdPartyChannelCubit(this._repository) : super(const ThirdPartyChannelState());
  final ThirdPartyChannelRepository _repository;
  final _bankRepository = getIt<BankRepository>();

  Future<void> getThirdPartyChannelList(int? type) async {
    emit(state.copyWith(channelListStatus: DataStatus.loading, updatingField: ThirdPartyChannelField.list));
    final result = await ThirdPartyServiceV2.getThirdPartyChannelList(type);
    emit(state.copyWith(
      channelListStatus: DataStatus.success,
      channels: result,
      error: null,
    ));
  }

  Future<void> submit({
    required int currentChannelIndex,
    required int selectedPaymentTypeIndex,
    required String amount,
  }) async {
    _deposit(
      currentChannelIndex: currentChannelIndex,
      selectedPaymentTypeIndex: selectedPaymentTypeIndex,
      amount: amount,
    );
  }

  void _deposit({
    required int currentChannelIndex,
    required int selectedPaymentTypeIndex,
    required String amount,
  }) async {
    if (!_validateAmount(currentChannelIndex, selectedPaymentTypeIndex, amount)) {
      return;
    }

    emit(state.copyWith(paymentStatus: DataStatus.loading, updatingField: ThirdPartyChannelField.payment));

    final amountValue = double.parse(amount);
    final channelId = state.channels[currentChannelIndex].payTypeList[selectedPaymentTypeIndex].payTypeId;

    final result = await _repository.doPayin(channelId: channelId, amount: amountValue);

    if (result.data != null) {
      emit(state.copyWith(
        paymentStatus: DataStatus.success,
        paymentResponse: result.data,
        error: null,
      ));
    } else {
      emit(state.copyWith(paymentStatus: DataStatus.failed, error: result.error));
    }
  }

  bool _validateAmount(
    int currentChannelIndex,
    int selectedPaymentTypeIndex,
    String amount,
  ) {
    if (state.channels.isEmpty) return false;
    final payType = state.channels[currentChannelIndex].payTypeList[selectedPaymentTypeIndex];
    final (amountMinLimit, amountMaxLimit) = (payType.amountMinLimit, payType.amountMaxLimit);
    final amountValue = double.tryParse(amount);
    if (amountValue == null || amountValue < amountMinLimit || amountValue > amountMaxLimit) {
      GPEasyLoading.showToast('amountRangeError'.tr(args: [amountMinLimit.toString(), amountMaxLimit.toString()]));
      return false;
    }
    return true;
  }

  // User Wallet Methods
  Future<void> getUserWalletList({String? bankCode}) async {
    if (state.walletsFetchStatus == DataStatus.loading) return;

    emit(state.copyWith(walletsFetchStatus: DataStatus.loading, updatingField: ThirdPartyChannelField.wallets));

    try {
      final result = await _bankRepository.getUserWalletList(bankCode: bankCode);

      if (result.isSuccess && result.data != null) {
        emit(state.copyWith(
          walletsFetchStatus: DataStatus.success,
          wallets: result.data,
          error: null,
        ));
      } else {
        emit(state.copyWith(
          walletsFetchStatus: DataStatus.failed,
          error: result.error ?? 'Failed to fetch user wallets',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        walletsFetchStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }

  void updateSelectedWallet(UserWalletModel? wallet) {
    emit(state.copyWith(selectedWallet: wallet));
  }

  Future<void> bindUserWallet({
    required String bankCode,
    required String payAddress,
  }) async {
    emit(state.copyWith(bindWalletStatus: DataStatus.loading, updatingField: ThirdPartyChannelField.bindWallet));

    try {
      final result = await _repository.bindUserWallet(
        bankCode: bankCode,
        payAddress: payAddress,
      );

      if (result.isSuccess) {
        emit(state.copyWith(
          bindWalletStatus: DataStatus.success,
          error: null,
        ));
        // Refresh wallet list after successful binding
        await getUserWalletList();
      } else {
        emit(state.copyWith(
          bindWalletStatus: DataStatus.failed,
          error: result.error ?? 'Failed to bind wallet',
        ));
      }
    } catch (e) {
      emit(state.copyWith(
        bindWalletStatus: DataStatus.failed,
        error: e.toString(),
      ));
    }
  }
}
