part of 'third_party_channel_cubit.dart';

enum ThirdPartyChannelField { list, payment, wallets, bindWallet }

class ThirdPartyChannelState extends Equatable {
  final DataStatus channelListStatus;
  final DataStatus paymentStatus;
  final DataStatus walletsFetchStatus;
  final DataStatus bindWalletStatus;
  final String? error;
  final ThirdPartyChannelField? updatingField;
  final List<ThirdPartyChannelEntity> channels;
  final ThirdPartySuccessResponse? paymentResponse;
  final List<UserWalletModel>? wallets;
  final UserWalletModel? selectedWallet;

  const ThirdPartyChannelState({
    this.channelListStatus = DataStatus.idle,
    this.paymentStatus = DataStatus.idle,
    this.walletsFetchStatus = DataStatus.idle,
    this.bindWalletStatus = DataStatus.idle,
    this.error,
    this.updatingField,
    this.channels = const <ThirdPartyChannelEntity>[],
    this.paymentResponse,
    this.wallets,
    this.selectedWallet,
  });

  @override
  List<Object?> get props => [
        channelListStatus,
        paymentStatus,
        walletsFetchStatus,
        bindWalletStatus,
        error,
        updatingField,
        channels,
        paymentResponse,
        wallets,
        selectedWallet,
      ];

  ThirdPartyChannelState copyWith({
    DataStatus? channelListStatus,
    DataStatus? paymentStatus,
    DataStatus? walletsFetchStatus,
    DataStatus? bindWalletStatus,
    String? error,
    ThirdPartyChannelField? updatingField,
    List<ThirdPartyChannelEntity>? channels,
    ThirdPartySuccessResponse? paymentResponse,
    List<UserWalletModel>? wallets,
    UserWalletModel? selectedWallet,
  }) {
    return ThirdPartyChannelState(
      channelListStatus: channelListStatus ?? this.channelListStatus,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      walletsFetchStatus: walletsFetchStatus ?? this.walletsFetchStatus,
      bindWalletStatus: bindWalletStatus ?? this.bindWalletStatus,
      error: error ?? this.error,
      updatingField: updatingField ?? this.updatingField,
      channels: channels ?? this.channels,
      paymentResponse: paymentResponse ?? this.paymentResponse,
      wallets: wallets ?? this.wallets,
      selectedWallet: selectedWallet ?? this.selectedWallet,
    );
  }
}
