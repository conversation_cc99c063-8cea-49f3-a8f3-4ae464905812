import 'package:gp_stock_app/generated/json/base/json_field.dart';
import 'package:gp_stock_app/generated/json/third_party_channel_entity_list_entity.g.dart';
import 'dart:convert';
export 'package:gp_stock_app/generated/json/third_party_channel_entity_list_entity.g.dart';

@JsonSerializable()
class ThirdPartyChannelListEntity {
	List<ThirdPartyChannelEntity> list = [];

	ThirdPartyChannelListEntity();

	factory ThirdPartyChannelListEntity.fromJson(Map<String, dynamic> json) => $ThirdPartyChannelListEntityFromJson(json);

	Map<String, dynamic> toJson() => $ThirdPartyChannelListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ThirdPartyChannelEntity {
	String currency = "";
	String icon = "";
	List<ThirdPartyChannelPayType> payTypeList = [];
	String payWayCode = "";
	String payWayName = "";
	bool recommended = false;
	bool isNeedBind = false; // 是否需要绑定

	ThirdPartyChannelEntity();

	factory ThirdPartyChannelEntity.fromJson(Map<String, dynamic> json) => $ThirdPartyChannelEntityFromJson(json);

	Map<String, dynamic> toJson() => $ThirdPartyChannelEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ThirdPartyChannelPayType {
	String amountList = "";
	int amountMaxLimit = 0;
	int amountMinLimit = 0;
	String controllerTips = "";
	int exchangeRate = 0;
	bool fixedAmount = false;
	int payTypeId = 0;
	String payTypeName = "";
	int sort = 0;

	ThirdPartyChannelPayType();

	factory ThirdPartyChannelPayType.fromJson(Map<String, dynamic> json) => $ThirdPartyChannelPayTypeFromJson(json);

	Map<String, dynamic> toJson() => $ThirdPartyChannelPayTypeToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}