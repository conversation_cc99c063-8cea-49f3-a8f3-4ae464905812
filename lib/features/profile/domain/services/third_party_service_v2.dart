import 'package:gp_stock_app/core/api/network/endpoint/api_endpoints.dart';
import 'package:gp_stock_app/core/services/http/http.dart';
import 'package:gp_stock_app/features/profile/domain/models/third_party_channel_entity_list_entity.dart';

class ThirdPartyServiceV2 {
  static Future<List<ThirdPartyChannelEntity>> getThirdPartyChannelList(int? type) async {
    final response = await Http().request<ThirdPartyChannelListEntity>(
      ApiEndpoints.thirdPartyChannelList,
      method: HttpMethod.get,
      queryParameters: {
        if (type != null) 'type': type,
      },
    );
    if (response.isSuccess && response.data != null) {
      return response.data!.list;
    } else {
      return [];
    }
  }
}
