import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/entities/account.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/services/user/user_state.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';

import 'profile_widget.dart';

class ProfileHeader extends StatefulWidget {
  const ProfileHeader({super.key});

  @override
  State<ProfileHeader> createState() => _ProfileHeaderState();
}

class _ProfileHeaderState extends State<ProfileHeader> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 265.gh,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          AngleBottomWidget(
            child: Positioned(
              top: 65.gh,
              left: 16.gw,
              right: 16.gw,
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: ProfileDataWidget(),
              ),
            ),
          ),
          Positioned(
            top: 140.gh,
            left: 16.gw,
            right: 16.gw,
            child: SlideTransition(
              position: _slideAnimation,
              child: Container(
                decoration: BoxDecoration(
                  color: context.theme.cardColor,
                  borderRadius: BorderRadius.circular(8.gr),
                ),
                child: Padding(
                  padding: EdgeInsets.all(16.gr),
                  child: BlocSelector<UserCubit, UserState, AccountInfo?>(
                    selector: (state) => state.accountInfo,
                    builder: (context, state) {
                      final usableCash = state?.usableCash ?? 0.00;
                      final interestCash = state?.interestCash ?? 0.00;
                      final frozenAmount = state?.freezeCash ?? 0.00;
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  HeaderItem(
                                    context: context,
                                    title: 'availableBalance'.tr(),
                                    value: usableCash,
                                    isCurrency: true,
                                  ),
                                  GestureDetector(
                                    onTap: () => AuthUtils.verifyAuth(
                                      () => getIt<NavigatorService>().push(AppRouter.routeInterestRecord),
                                    ),
                                    child: HeaderItem(
                                      context: context,
                                      title: 'interest'.tr(),
                                      value: interestCash,
                                      showArrow: true,
                                    ),
                                  ),
                                ],
                              ),
                              GestureDetector(
                                onTap: () => getIt<NavigatorService>().push(AppRouter.routeFundRecords),
                                child: Container(
                                  padding: EdgeInsets.symmetric(horizontal: 10.gw, vertical: 5.gh),
                                  decoration: BoxDecoration(
                                    color: context.theme.primaryColor,
                                    borderRadius: BorderRadius.circular(4.gr),
                                  ),
                                  child: Text(
                                    'fundRecords'.tr(),
                                    style: context.textTheme.primary.fs10.copyWith(
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          10.verticalSpace,
                          Container(
                            height: 40.gh,
                            width: 315.gw,
                            decoration: BoxDecoration(
                              color: context.theme.inputDecorationTheme.fillColor,
                              borderRadius: BorderRadius.circular(8.gr),
                            ),
                            child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.gr),
                              child: Row(
                                spacing: 16.gw,
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    'frozen'.tr(),
                                    style: context.textTheme.regular.w500,
                                  ),
                                  FlipText(
                                    frozenAmount,
                                    style: context.textTheme.regular.fs16.w800.ffAkz,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
