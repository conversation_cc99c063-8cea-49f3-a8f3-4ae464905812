import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class MarketTableHeaderStyleD extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback onTap;
  final bool disableScaleAnimation;

  const MarketTableHeaderStyleD({
    super.key,
    required this.title,
    required this.isSelected,
    required this.onTap,
    this.disableScaleAnimation = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        decoration: BoxDecoration(
          color: isSelected ? context.theme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          title,
          style: context.textTheme.regular.w500.copyWith(
            color: isSelected ? Colors.white : context.colorTheme.textRegular,
          ),
        ),
      ),
    );
  }
}
