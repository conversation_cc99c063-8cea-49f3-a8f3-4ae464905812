import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/models/stock/stock_response.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/widgets/market_table/index_trade_row.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class VisualGraphList extends StatefulWidget {
  const VisualGraphList({super.key});

  @override
  State<VisualGraphList> createState() => _VisualGraphListState();
}

class _VisualGraphListState extends State<VisualGraphList> with SingleTickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<IndexTradeCubit, IndexTradeState>(
      builder: (context, state) {
        int itemCount = state.indexStocks.length;

        if (state.status == DataStatus.loading) {
          return _buildLoadingList();
        }

        if (state.status.isFailed) {
          return const Center(child: TableEmptyWidget());
        }

        if (state.status == DataStatus.success && itemCount <= 0) {
          return const Center(child: TableEmptyWidget());
        }

        if (itemCount > 5) {
          itemCount = 5;
        }

        return ListView.builder(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: itemCount,
          itemBuilder: (context, index) {
            final item = state.indexStocks[index].stockInfo.data!;
            return IndexTradeRow(
              data: StockItem(
                name: item.name,
                symbol: item.symbol,
                latestPrice: item.latestPrice,
                market: item.market,
                gain: item.gain,
                chg: item.chg,
                securityType: item.securityType,
              ),
              onTap: () {
                AuthUtils.verifyAuth(() {
                  getIt<NavigatorService>().push(AppRouter.routeTradingCenter,
                    arguments: TradingArguments(
                      instrumentInfo: item.instrumentInfo,
                      selectedIndex: TradeTabType.Quotes.index,
                      shouldNavigateToIndex: true,
                      isIndexTrading: true,
                    ),
                  );
                });
              },
            );
          },
        );
      },
    );
  }

  Widget _buildLoadingList() {
    return Column(
      children: List.generate(
        6,
        (_) => Padding(
          padding: EdgeInsets.only(bottom: 8.gh),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8.gr),
            child: ShimmerWidget(
              height: 45.gh,
              width: double.infinity,
            ),
          ),
        ),
      ),
    );
  }
}
