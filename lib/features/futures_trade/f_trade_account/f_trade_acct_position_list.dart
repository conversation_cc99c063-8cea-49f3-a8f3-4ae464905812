import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account/widgets/table_loading.dart';
import 'package:gp_stock_app/features/account_v2/0_home/domain/models/account_market.dart';
import 'package:gp_stock_app/features/account_v2/spot/sub_screen/widgets/list_view_cell/account_position_cell.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_account/domain/f_trade_acct_order_model.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class FTradeAcctPositionList extends StatefulWidget {
  final DataStatus dataStatus;
  final FTradeAcctOrderModel? orderModel;
  final RefreshController refreshCtrl;

  /// actionType=> 'refresh' 'loadMore' 'clickRecordCell'
  final Function(String actionType, FTradeAcctOrderRecords record) onUserActions;
  const FTradeAcctPositionList({
    super.key,
    required this.dataStatus,
    required this.orderModel,
    required this.refreshCtrl,
    required this.onUserActions,
  });

  @override
  State<FTradeAcctPositionList> createState() => _FTradeAcctPositionListState();
}

class _FTradeAcctPositionListState extends State<FTradeAcctPositionList> {
  @override
  Widget build(BuildContext context) {
    final status = widget.dataStatus;
    final orderModel = widget.orderModel;

    if (status == DataStatus.loading) {
      return const TableLoadingState();
    }

    if (orderModel == null || orderModel.records.isEmpty) {
      return const TableEmptyWidget();
    }

    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: orderModel.records.length,
      itemBuilder: (_, index) {
        final isLast = index == orderModel.records.length - 1;
        final data = orderModel.records[index];
        return AnimationConfiguration.staggeredList(
          position: index,
          duration: const Duration(milliseconds: 600),
          child: SlideAnimation(
            verticalOffset: 30.0,
            child: FadeInAnimation(
              child: Padding(
                padding: EdgeInsets.only(bottom: isLast ? 0 : 16),
                child: AccountPositionCell(
                  marketCategory: MarketCategory.cnFutures,
                  data: data,
                  onTapDetail: () => widget.onUserActions('tapDetail', data),
                  onTap: () => widget.onUserActions('tap', data),
                  onTapTpSL: () => widget.onUserActions('tpsl', data),
                  onTapAdd: () => widget.onUserActions('add', data),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
