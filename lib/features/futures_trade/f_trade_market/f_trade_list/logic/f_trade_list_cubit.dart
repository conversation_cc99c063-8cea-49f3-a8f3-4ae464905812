import 'dart:async';
import 'dart:convert';
import 'package:bloc/bloc.dart';
import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_model.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_repository.dart';
import 'package:gp_stock_app/features/futures_trade/f_trade_market/f_trade_list/domain/f_trade_list_service.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';

part 'f_trade_list_state.dart';

/// 持有和更新一个交易所的数据,其他未显示交易所的数据放入缓存
class FTradeListCubit extends Cubit<FTradeListState> {
  final bool showInHomePage;
  FTradeListCubit(this.fTradeListRepo, {required this.showInHomePage}) : super(FTradeListState());
  final FTradeListRepository fTradeListRepo;

  Timer? _pollTimer;
  Map<String, dynamic> _pollQueryParameters = {"": ""};
  int _pollFExchangeIdx = 0;

  CancelToken? _cancelToken;

  @override
  Future<void> close() {
    _cancelToken?.cancel();
    _pollTimer?.cancel();
    return super.close();
  }

  void startPolling() {
    _pollTimer?.cancel();
    _pollTimer = Timer.periodic(const Duration(milliseconds: 3500), (_) => _pollMarket());
  }

  // 获取页码*数量 轮询所有数据
  // 总数预计在100条以下, 如果超过100条此处需要重新设计
  // 暂无法计算是否开盘
  void _pollMarket() {
    final currentSelectedPageidx = getIt<MainCubit>().state.selectedNavigationItem.index;
    if (showInHomePage && currentSelectedPageidx != 0) {
      return;
    }
    if (showInHomePage == false && currentSelectedPageidx != 2) {
      return;
    }
    final newParams = jsonDecode(jsonEncode(_pollQueryParameters)) as Map<String, dynamic>;
    final pageNumber = _pollQueryParameters["pageNumber"] as int;
    final pageSize = _pollQueryParameters["pageSize"] as int;
    newParams["pageNumber"] = 1;
    newParams["pageSize"] = pageNumber * pageSize;
    _fetchAndSaveListData(
      fExchangeIdx: _pollFExchangeIdx,
      loadMore: false,
      queryParameters: newParams,
      needShowFlutterToast: false,
    );
  }

  void resetPolling() {
    startPolling();
  }

  void stopPolling() {
    _pollTimer?.cancel();
  }

  /// 获取列表第一页数据逻辑
  ///
  /// loadCache 决定loading效果
  ///
  /// ```
  ///[+]            NO      访问 HTTP
  ///|获取数据     +----->  -----+---->   +------+
  ///|             |             ^        |订阅  | +-+
  ///|             |             |        +------+   |
  ///v 检查缓存    | YES     缓存列表     |更新UI| <-+
  /// +--------> --+---->  ---------->    +------+
  ///```
  Future<void> fetchData({
    required int fExchangeIdx,
    required bool loadCache,
    required bool loadMore,
    required Map<String, dynamic> queryParameters,
  }) async {
    _pollFExchangeIdx = fExchangeIdx;
    _pollQueryParameters = queryParameters;

    if (loadCache) {
      final (cachedTotl, cachedItems) = fTradeListRepo.loadCacheData(fExchangeIdx);
      if (cachedItems.isNotEmpty) {
        emit(state.copyWith(
          totalNum: cachedTotl,
          items: cachedItems,
          status: DataStatus.success,
        ));
      } else {
        emit(state.copyWith(
          totalNum: 0,
          items: [],
          status: DataStatus.loading,
        ));
      }
    }
    _fetchAndSaveListData(fExchangeIdx: fExchangeIdx, loadMore: loadMore, queryParameters: queryParameters);
  }

  Future<void> _fetchAndSaveListData({
    required int fExchangeIdx,
    required bool loadMore,
    required Map<String, dynamic> queryParameters,
    bool needShowFlutterToast = true,
  }) async {
    _cancelToken?.cancel();
    _cancelToken = CancelToken();
    FTradeListService.fetchTableData(_cancelToken, queryParameters: queryParameters).then((result) {
      if (result != null) {
        emit(state.copyWith(
          totalNum: result.totalNum,
          items: loadMore ? state.items + result.list : result.list,
          status: DataStatus.success,
        ));
        fTradeListRepo.saveCacheData(
          fExchangeIdx,
          loadMore ? (result.totalNum, state.items + result.list) : (result.totalNum, result.list),
        );
      } else {
        if (!isClosed) {
          emit(state.copyWith(
            totalNum: state.totalNum,
            items: state.items,
            status: DataStatus.failed,
          ));
        }
      }
    });
  }
}
