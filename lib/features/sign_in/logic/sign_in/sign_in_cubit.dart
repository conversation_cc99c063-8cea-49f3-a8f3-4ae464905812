import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/api/network/network.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/models/apis/auth.dart';
import 'package:gp_stock_app/core/models/entities/user.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';
import 'package:gp_stock_app/core/utils/secure_storage_helper.dart';
import 'package:gp_stock_app/core/utils/wangyi_captcha_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/keys.dart';
import 'package:gp_stock_app/shared/services/web_socket/web_scoket_interface.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/utils/auth/auth_utils.dart';
import '../../../../shared/constants/enums.dart';

part 'sign_in_state.dart';

@lazySingleton
class SignInCubit extends Cubit<SignInState> {
  final WebSocketService _webSocketService;

  SignInCubit(this._webSocketService) : super(const SignInState());

  void selectTab(int index) {
    emit(state.copyWith(selectedTab: index));
  }

  Future<void> login({
    required String username,
    required String password,
    required String mobile,
    required String smsCode,
  }) async {
    emit(state.copyWith(loginFetchStatus: DataStatus.loading));
    final loginMode = LoginMode.values[state.selectedTab];
    final accountName = _getAccountName(loginMode, username, mobile);

    final (bool isSuccess, bool isNeedCaptcha) = await AuthApi.fetchWangYiCaptchaRequired(
      type: WangYiCaptchaType.kLogin,
    );
    if (!isSuccess) {
      emit(state.copyWith(loginFetchStatus: DataStatus.failed));
      return GPEasyLoading.showToast("get_verification_method_failed".tr());
    }
    if (isNeedCaptcha) {
      _handleCaptcha(accountName, password, smsCode);
    } else {
      _commitLogin(accountName, password, smsCode);
    }
  }

  String _getAccountName(LoginMode loginMode, String username, String mobile) {
    return loginMode == LoginMode.account ? username : mobile;
  }

  void _handleCaptcha(String accountName, String password, String smsCode) {
    WangYiCaptchaUtil().show(
      captchaId: AppConfig.instance.wangYiCaptchaKey,
      account: accountName,
      onSuccess: (code) => _commitLogin(accountName, password, smsCode, validate: code),
      onValidateFailClose: _onCaptchaValidationFailed,
      onError: _onCaptchaError,
    );
  }

  void _onCaptchaValidationFailed() {
    if (!isClosed) {
      emit(state.copyWith(loginFetchStatus: DataStatus.idle));
    }
  }

  void _onCaptchaError() {
    if (!isClosed) {
      GPEasyLoading.showToast('验证失败'.tr());
      emit(state.copyWith(loginFetchStatus: DataStatus.failed));
    }
  }

  Future<void> _commitLogin(String accountName, String password, String smsCode, {String? validate}) async {
    final model = await AuthApi.login(
      mobile: accountName,
      password: password,
      mode: LoginMode.values[state.selectedTab],
      smsCode: smsCode,
      validate: validate,
    );
    if (model != null) {
      _onLoginSuccess(model, accountName, password);
    } else {
      emit(state.copyWith(loginFetchStatus: DataStatus.failed));
    }
  }

  Future<void> _onLoginSuccess(UserModel model, String accountName, String password) async {
    final userCubit = getIt<UserCubit>();
    userCubit.setUserInfo(model);
    if (_shouldRememberPassword()) {
      await AuthUtils.instance.rememberPassword(true, username: accountName, password: password);
    }
    _webSocketService.send({'type': "auth", 'data': userCubit.state.token});
    emit(state.copyWith(loginFetchStatus: DataStatus.success));
  }

  bool _shouldRememberPassword() {
    return LoginMode.values[state.selectedTab] == LoginMode.account && state.isRememberPassword;
  }

  void init() {
    SecureStorageHelper().readSecureData(LocalStorageKeys.userV2).then((value) async {
      if (value != null) {
        final token = await SecureStorageHelper().readSecureData(LocalStorageKeys.token);
        _webSocketService.send({
          'type': "auth",
          'data': token,
        });
        emit(
          state.copyWith(loginFetchStatus: DataStatus.success),
        );
      }
    });
  }

  Future<void> logout() async {
    await SecureStorageHelper().deleteAllExcept();
    NetworkProvider.clearCache();
    emit(state.copyWith(clearData: true));
  }

  Future<void> rememberPassword(bool value, {String? username, String? password}) async {
    emit(state.copyWith(isRememberPassword: value));
    if (value) {
      await AuthUtils.instance.rememberPassword(value, username: username, password: password);
    } else {
      await AuthUtils.instance.clearRememberedCredentials();
    }
  }

  Future<(bool, String?, String?)> getRememberPassword() async {
    final value = await AuthUtils.instance.isRememberPassword;
    emit(state.copyWith(isRememberPassword: value.$1));
    return value;
  }

  void acceptTerms(bool value) {
    emit(state.copyWith(isAcceptedTerms: value));
  }

  Future<void> requestOTP({required String phoneNumber, required String sendType}) async {
    emit(state.copyWith(otpReqStatus: DataStatus.loading));
    final flag = await AuthApi.fetchOTP(phoneNumber: phoneNumber, sendType: sendType);
    if (flag) {
      emit(state.copyWith(otpReqStatus: DataStatus.success));
    } else {
      emit(state.copyWith(otpReqStatus: DataStatus.failed));
    }
  }
}
