import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/constants/kline_constants.dart';
import 'package:gp_stock_app/features/account/domain/models/account_summary/contract_summary_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/entrustment_widget.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/position_widget.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_bottomsheet_orders.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/features/account/widgets/table_loading.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/logic/account_info/account_info_cubit.dart';
import 'package:gp_stock_app/shared/models/instrument/instrument.dart';
import 'package:gp_stock_app/shared/models/route_arguments/trading_arguments.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/widgets/market_table/market_table_row1.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../shared/widgets/pagination/common_refresher.dart';
import '../../market/widgets/market_table_header.dart';
import '../domain/models/order/order_response.dart';
import '../logic/account/account_cubit.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

///
class AccountMarketTable extends StatelessWidget {
  const AccountMarketTable({super.key, this.contract});

  final ContractSummaryData? contract;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const TableTabsSection(),
        8.verticalSpace,
        TableContentSection(contract: contract),
      ],
    );
  }
}

class TableTabsSection extends StatelessWidget {
  const TableTabsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Padding(
            padding: EdgeInsets.only(top: 16.gh),
            child: AnimationLimiter(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: AnimationConfiguration.toStaggeredList(
                  duration: const Duration(milliseconds: 300),
                  childAnimationBuilder: (widget) => SlideAnimation(
                    horizontalOffset: 50.0,
                    child: FadeInAnimation(
                      child: widget,
                    ),
                  ),
                  children: _buildTabHeaders(context, state),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  List<Widget> _buildTabHeaders(BuildContext context, AccountState state) {
    final headers = AccountMarketType.values.map((type) => type.translationKey).toList();
    return headers.asMap().entries.expand((entry) {
      final index = entry.key;
      final header = entry.value;
      final type = AccountMarketType.values[index];
      final count = switch (type) {
        AccountMarketType.currentPositions => state.currentPositions?.total ?? 0,
        AccountMarketType.tradeDetails => state.tradeDetails?.total ?? 0,
        AccountMarketType.orderDetails => state.orderDetails?.total ?? 0,
      };

      return [
        if (index > 0) 15.horizontalSpace,
        SizedBox(
          width: 100.gw,
          child: MarketTableHeader(
            title: '${header.tr()} ($count)',
            isSelected: state.selectedTableTab == type,
            onTap: () => context.read<AccountCubit>().updateTableTab(type),
          ),
        ),
      ];
    }).toList();
  }
}

class TableContentSection extends StatelessWidget {
  const TableContentSection({super.key, this.contract});
  final ContractSummaryData? contract;
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: context.theme.cardColor,
        borderRadius: BorderRadius.circular(8.gr),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TableHeaderSection(),
          TableBodySection(contract: contract),
        ],
      ),
    );
  }
}

class TableHeaderSection extends StatelessWidget {
  const TableHeaderSection({super.key});

  @override
  Widget build(BuildContext context) {
    final headerTitles2 = [
      '${'name'.tr()} | ${'code'.tr()}',
      '${'prices'.tr()} | ${'quantity'.tr()}',
      'sumOfMoneySold'.tr(),
      '${'direction'.tr()} | ${'time'.tr()}',
    ];
    final headerTitles3 = [
      '${'name'.tr()} |\n ${'code'.tr()}',
      'orderPrice'.tr(),
      '${'completed'.tr()} |\n ${'total'.tr()}',
      '${'direction'.tr()} |\n ${'status'.tr()}',
      ('operate'.tr()),
    ];

    final flexValues = [3, 2, 2, 2, 2];

    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        final headerTitles = state.selectedTableTab == AccountMarketType.tradeDetails
            ? headerTitles2
            : state.selectedTableTab == AccountMarketType.orderDetails
                ? headerTitles3
                : [];

        return state.selectedTableTab != AccountMarketType.currentPositions
            ? Column(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(
                      horizontal: 10.gw,
                      vertical: 12.gh,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        for (var i = 0; i < headerTitles.length; i++) ...[
                          if (i > 0) 5.horizontalSpace,
                          Expanded(
                              flex: flexValues[i],
                              child: Tooltip(
                                message: headerTitles[i],
                                child: Text(
                                  headerTitles[i],
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  textAlign: i == 0 ? TextAlign.left :  i == headerTitles.length - 1 ? TextAlign.end : TextAlign.center,
                                  style: context.textTheme.regular.fs12.copyWith(
                                    color: context.colorTheme.textRegular,
                                  ),
                                ),
                              )),
                        ],
                      ],
                    ),
                  ),
                  Divider(
                    color: context.theme.dividerColor,
                    height: 1,
                  ),
                ],
              )
            : const SizedBox.shrink();
      },
    );
  }
}

class TableBodySection extends StatelessWidget {
  TableBodySection({super.key, this.contract});
  final ContractSummaryData? contract;
  final RefreshController _refreshController = RefreshController(initialRefresh: false);

  void _onRefresh({required AccountMarketType type, required BuildContext context}) {
    final accountCubit = context.read<AccountCubit>();
    final assetId = context.read<AccountInfoCubit>().state.accountInfo?.assetId.toString();
    accountCubit.getOrderList(type,
        commentAssetId: assetId, contractId: contract?.id);
    _refreshController.resetNoData();
    _refreshController.refreshCompleted();
  }

  void _onLoading({
    required BuildContext context,
    required RefreshController refreshController,
  }) {
    AccountMarketType type = context.read<AccountCubit>().state.selectedTableTab;
    OrderData? records = switch (type) {
      AccountMarketType.currentPositions => context.read<AccountCubit>().state.currentPositions,
      AccountMarketType.tradeDetails => context.read<AccountCubit>().state.tradeDetails,
      AccountMarketType.orderDetails => context.read<AccountCubit>().state.orderDetails,
    };

    context.read<AccountCubit>().state.currentPositions;

    if (records == null || records.records == null) {
      refreshController.loadNoData();
      return;
    }

    final currentLength = records.records?.length ?? 0;
    final totalItems = records.total ?? 0;

    if (currentLength >= totalItems) {
      refreshController.loadNoData();
      return;
    }
    final accountCubit = context.read<AccountCubit>();
    final assetId = context.read<AccountInfoCubit>().state.accountInfo?.assetId.toString();
    accountCubit.getOrderList(
      type,
      isLoadMore: true,
      commentAssetId: assetId,
      contractId: contract?.id,
    );
    refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AccountCubit, AccountState>(
      builder: (context, state) {
        final status = _getTableStatus(state);
        final items = _getTableItems(state);

        if (status == DataStatus.loading) {
          return const TableLoadingState();
        }

        if (items == null || (items.records?.isEmpty ?? true)) {
          return const TableEmptyWidget();
        }

        return SizedBox(
          height: .45.gsh,
          child: CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            enablePullUp: true,
            onRefresh: () => _onRefresh(
              type: state.selectedTableTab,
              context: context,
            ),
            onLoading: () => _onLoading(
              context: context,
              refreshController: _refreshController,
            ),
            child: ListView.separated(
              physics: const AlwaysScrollableScrollPhysics(),
              shrinkWrap: true,
              padding: EdgeInsets.only(bottom: 16.gh),
              itemCount: items.records?.length ?? 0,
              separatorBuilder: (_, __) => Divider(
                color: context.theme.dividerColor,
                height: 1,
              ),
              itemBuilder: (_, index) => switch (state.selectedTableTab) {
                AccountMarketType.currentPositions => PositionTableRow(
                    data: items.records?[index] ?? OrderRecord(),
                    contract: contract,
                  ),
                AccountMarketType.tradeDetails => TradeDetailsRow(
                    data: items.records?[index] ?? OrderRecord(),
                    contract: contract,
                  ),
                AccountMarketType.orderDetails => EntrustmentRow(
                    data: items.records?[index] ?? OrderRecord(),
                    contract: contract,
                  ),
              },
            ),
          ),
        );
      },
    );
  }

  DataStatus _getTableStatus(AccountState state) {
    return switch (state.selectedTableTab) {
      AccountMarketType.currentPositions => state.currentPositionsFetchStatus,
      AccountMarketType.tradeDetails => state.tradeDetailsFetchStatus,
      AccountMarketType.orderDetails => state.orderDetailsFetchStatus,
    };
  }

  OrderData? _getTableItems(AccountState state) {
    return switch (state.selectedTableTab) {
      AccountMarketType.currentPositions => state.currentPositions,
      AccountMarketType.tradeDetails => state.tradeDetails,
      AccountMarketType.orderDetails => state.orderDetails,
    };
  }
}

class PositionTableRow extends StatelessWidget {
  const PositionTableRow({super.key, required this.data, required this.contract});
  final OrderRecord data;
  final ContractSummaryData? contract;

  @override
  Widget build(BuildContext context) {
    return PositionWidget(
      data: data,
      onTap: () {
        getIt<NavigatorService>().push(AppRouter.routeTradingCenter,
          arguments: TradingArguments(
            instrumentInfo: Instrument(
              instrument: data.instrument,
            ),
            selectedIndex: TradeTabType.Quotes.index,
            contract: contract,
            isIndexTrading: data.isIndex,
          ),
        ).then((value) {
          if (!context.mounted) return;
          context.read<AccountCubit>().getOrderList(
                AccountMarketType.currentPositions,
                symbol: data.symbol,
                market: data.market,
                securityType: data.securityType,
                commentAssetId: (context.read<AccountInfoCubit>().state.accountInfo?.assetId ?? 0).toString(),
                contractId: contract?.id,
                // passing isPolling & isLoadMore as true to skip loading
                isPolling: true,
                isLoadMore: true,
              );
        });
      },
    );
  }
}

class EntrustmentRow extends StatelessWidget {
  const EntrustmentRow({
    super.key,
    required this.data,
    this.showRevokeButton = true,
    this.contract,
  });

  final OrderRecord data;
  final bool showRevokeButton;
  final ContractSummaryData? contract;
  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TradingCubit(),
      child: Builder(builder: (context) {
        return BlocListener<TradingCubit, TradingState>(
          listenWhen: (previous, current) => previous.orderCancelStatus != current.orderCancelStatus,
          listener: (context, state) {
            if (state.orderCancelStatus == DataStatus.loading) {
              GPEasyLoading.showLoading(message: 'processingTrade'.tr());
            }
            if (state.orderCancelStatus == DataStatus.success || state.orderCancelStatus == DataStatus.failed) {
              GPEasyLoading.dismiss();
              if (state.orderCancelStatus == DataStatus.success) {
                GPEasyLoading.showSuccess(message: 'orderCancelSuccess'.tr());
                context.read<AccountCubit>().getOrderList(
                      AccountMarketType.currentPositions,
                      symbol: data.symbol,
                      market: data.market,
                      securityType: data.securityType,
                    );
                context.read<AccountCubit>().getOrderList(
                      AccountMarketType.orderDetails,
                      symbol: data.symbol,
                      market: data.market,
                      securityType: data.securityType,
                      status: data.status,
                    );
              } else if (state.orderCancelStatus == DataStatus.failed) {
                GPEasyLoading.showToast(state.error);
              }
            }
          },
          child: GestureDetector(
            onTap: () {
              context.read<TradingCubit>().getKlineDetailList(data.instrument, KlineConstants.options[0]);
              showModalBottomSheet(
                context: context,
                isScrollControlled: true,
                builder: (_) {
                  return BlocProvider.value(
                    value: context.read<TradingCubit>(),
                    child: TradeBottomsheetOrders(
                      data: data,
                      isTradeDetails: false,
                      isTrading: false,
                      contract: contract,
                    ),
                  );
                },
              );
            },
            child: EntrustmentWidget(data: data, showRevokeButton: showRevokeButton),
          ),
        );
      }),
    );
  }
}
