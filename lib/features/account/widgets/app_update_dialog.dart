import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/system_util.dart';
import 'package:gp_stock_app/features/home/<USER>/models/app_update/app_update.dart';
import 'package:gp_stock_app/shared/app/extension/color_extension.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

/// App更新弹窗
class AppUpdateDialog {
  final AppUpdate model;

  AppUpdateDialog({required this.model});

  OverlayEntry? _overlayEntry;

  void show() {
    final context = getIt<NavigatorService>().navigatorKey.currentContext!;
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder: (BuildContext context) => Material(
        color: Colors.black.withNewOpacity(0.4),
        child: _AppUpdateDialogContent(
          model: model,
          onDismiss: () {
            _overlayEntry?.remove();
            _overlayEntry = null;
          },
        ),
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void dismiss() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}

class _AppUpdateDialogContent extends StatelessWidget {
  final AppUpdate model;

  /// 关闭回调
  final VoidCallback onDismiss;

  const _AppUpdateDialogContent({required this.model, required this.onDismiss});

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Center(
        child: Container(
          width: 334.gw,
          constraints: BoxConstraints(
            minHeight: 250.gw,
            maxHeight: 400.gw,
          ),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color(0xFF2662FF),
                Color(0xFF91D5FF),
              ],
            ),
            border: Border.all(
              color: Color(0xFF18CDFF),
              width: 1,
            ),
            borderRadius: BorderRadius.circular(18), // 如果需要圆角可设置
          ),
          child: Padding(
              padding: EdgeInsets.fromLTRB(6.gw, 6.gw, 6.gw, 8.gw), // 根据需求调整
              child: Stack(
                clipBehavior: Clip.none,
                fit: StackFit.expand,
                children: [
                  Positioned(
                      top: 0,
                      left: 20,
                      child: Image.asset(
                        "assets/images/text_app_update.png",
                        width: 290.gw,
                        height: 41.gw,
                      )),
                  Positioned(
                      top: -30.gw,
                      right: -5.gw,
                      child: Image.asset(
                        "assets/images/icon_app_update.png",
                        width: 102.gw,
                        height: 102.gw,
                      )),
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 8.gw),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "update_available".tr(),
                              style: TextStyle(fontSize: 26, fontWeight: FontWeight.w700, color: Colors.white),
                            ),
                            SizedBox(height: 4.gw),
                            Text(
                              "${"new_version_detected".tr()} ${model.version}, ${"download_now".tr()}",
                              style: TextStyle(fontSize: 14, color: Colors.white),
                            ),
                            SizedBox(height: 4.gw),
                          ],
                        ),
                      ),
                      Expanded(
                        child: Container(
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.all(Radius.circular(18.gw)),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: 18.gw),
                              Expanded(
                                child: Padding(
                                  padding: EdgeInsets.symmetric(horizontal: 14.gw),
                                  child: SingleChildScrollView(
                                    child: Text(
                                      model.content,
                                      style: TextStyle(fontSize: 14, color: Color(0xff020202)),
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox(height: 35.gw),
                              Padding(
                                padding: EdgeInsets.fromLTRB(16.gw, 10.gw, 16.gw, 11.gw),
                                child: Row(
                                  children: [
                                    if (model.isForce == 0) ...[
                                      _getButton(
                                          title: "update_later".tr(),
                                          isUpdateBtn: false,
                                          onTap: () {
                                            onDismiss.call();
                                          }),
                                      SizedBox(width: 13.gw),
                                    ],
                                    _getButton(title: "update_now".tr(), isUpdateBtn: true, onTap: () {
                                      if (model.isForce == 0) {
                                        onDismiss.call();
                                      }
                                      SystemUtil.openUrlOnSystemBrowser(url: model.url);
                                    }),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              )),
        ),
      ),
    );
  }

  Widget _getButton({required String title, required bool isUpdateBtn, required GestureTapCallback onTap}) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          height: 43.gw,
          decoration: isUpdateBtn
              ? BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFF2662FF), Color(0xFF2EB9FF)],
                  ),
                  borderRadius: BorderRadius.circular(21.5),
                  boxShadow: const [
                    BoxShadow(
                      color: Color(0xFFC3E2FF),
                      offset: Offset(1, 4),
                      blurRadius: 6,
                    ),
                  ],
                )
              : BoxDecoration(
                  color: Color(0xffCACED9),
                  borderRadius: BorderRadius.circular(21.5),
                ),
          alignment: Alignment.center,
          child: Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              height: 1.0,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}
