import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';

import '../../../shared/constants/assets.dart';
import '../../../shared/constants/enums.dart';
import '../../../shared/routes/app_router.dart';
import '../widgets/build_action_buttons.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class ActionButtonsRowType1 extends StatelessWidget {
  const ActionButtonsRowType1({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        BuildActionButton(
          label: 'topUpDeposit'.tr(),
          icon: Assets.myAssetIcon,
          onTap: () {
            AuthUtils.verifyAuth(
              () => getIt<NavigatorService>().push(AppRouter.routeDepositMain),
            );
          },
        ),
        BuildActionButton(
          label: 'cashOut'.tr(),
          icon: Assets.withdrawIcon,
          onTap: () {
            AuthUtils.verifyAuth(
              () => context.verifyRealName(
                () => getIt<NavigatorService>().push(AppRouter.routeWithdrawMain),
              ),
            );
          },
        ),
      ],
    );
  }
}

class ActionButtonsRowType2 extends StatelessWidget {
  const ActionButtonsRowType2({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        BuildActionButton(
          label: 'applyContract2'.tr(),
          icon: Assets.contractIcon,
          onTap: () {
            AuthUtils.verifyAuth(() async {
              await getIt<NavigatorService>().push(AppRouter.routeContractApply,
                arguments: {'mainContractType': InstrumentType.stock},
              );
            });
          },
        ),
        BuildActionButton(
          label: 'apply_records'.tr(),
          icon: Assets.recordsIcon,
          onTap: () => context.verifyRealName(() => getIt<NavigatorService>().push(AppRouter.routeContractApplyRecord)),
        ),
        BuildActionButton(
          label: 'historicalMessages'.tr(),
          icon: Assets.historyIcon,
          onTap: () => context.verifyRealName(() => getIt<NavigatorService>().push(AppRouter.routeContractSettleHistory)),
        ),
      ],
    );
  }
}
