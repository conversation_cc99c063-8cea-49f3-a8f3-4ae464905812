import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/shared/widgets/dropdown/app_dropdown.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

/// A widget that represents the index trading section of the trading center.
/// This widget allows users to manage and execute index trading operations.
class IndexTradeSection extends StatefulWidget {
  /// Creates an instance of [IndexTradeSection].
  const IndexTradeSection({super.key});

  @override
  State<IndexTradeSection> createState() => _IndexTradeSectionState();
}

/// The state for the [IndexTradeSection] widget.
/// Manages the timer selection and position trading functionality.
class _IndexTradeSectionState extends State<IndexTradeSection> {
  /// The currently selected timer value in minutes.
  /// Will be updated when user selects a value from dropdown.
  int? selectedTimerValue;

  /// Whether a timer value has been selected from the dropdown.
  bool isTimerValueSelected = false;

  @override
  Widget build(BuildContext context) {
    final positions =
        context.select<AccountCubit, List<OrderRecord>?>((state) => state.state.currentPositions?.records) ?? [];
    final selectedPosition = context.select<TradingCubit, OrderRecord?>((state) => state.state.selectedPositionTimed);

    // Get the currently selected index and its timer values
    final selectedIndex = context.select((IndexTradeCubit cubit) {
      if (cubit.state.indexes.isEmpty) return null;
      return cubit.state.indexes[cubit.state.selectedIndex];
    });

    // Parse timer values from the selected index's timerValue string
    List<int> timerOptions = [];
    if (selectedIndex?.timerValue.isNotEmpty == true) {
      try {
        timerOptions = selectedIndex!.timerValue
            .split(',')
            .map((e) => int.tryParse(e.trim()))
            .where((e) => e != null)
            .cast<int>()
            .toList();
      } catch (e) {
        // If parsing fails, use empty list
        timerOptions = [];
      }
    }
    return BlocListener<TradingCubit, TradingState>(
      listenWhen: (previous, current) => previous.setExpireTimeStatus != current.setExpireTimeStatus,
      listener: (context, state) {
        if (state.setExpireTimeStatus.isLoading) {
          GPEasyLoading.showLoading(message: '正在执行交易...');
        }
        if (state.setExpireTimeStatus.isSuccess) {
          GPEasyLoading.showSuccess(message: '订单交易成功');
          final indexState = context.read<IndexTradeCubit>().state;
          context.read<AccountCubit>()
            ..getOrderList(
              AccountMarketType.currentPositions,
              symbol: indexState.indexes[indexState.selectedIndex].symbol,
              market: indexState.indexes[indexState.selectedIndex].market,
              securityType: indexState.indexes[indexState.selectedIndex].securityType,
            )
            ..getContractSummary()
            ..getOrderList(
              AccountMarketType.orderDetails,
              symbol: indexState.indexes[indexState.selectedIndex].symbol,
              status: 0,
              market: indexState.indexes[indexState.selectedIndex].market,
              securityType: indexState.indexes[indexState.selectedIndex].securityType,
            );
        }
        if (state.setExpireTimeStatus.isFailed) {
          GPEasyLoading.showToast(state.error);
        }
      },
      child: Column(
        spacing: 8.gh,
        children: [
          AppDropdown<OrderRecord>(
            hintText: 'selectPosition'.tr(),
            items: positions.map(
              (e) {
                final tradeTypeOption = TradeTypeOption.fromValue(e.tradeType ?? 1);
                final label =
                    ' ${e.id} | 均价 ${e.buyAvgPrice?.toStringAsFixed(2) ?? ''} | 总价 ${e.buyTotalNum ?? ''} | 可平 ${e.restNum ?? ''}';
                return DropdownMenuItem<OrderRecord>(
                  value: e,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 3.0),
                          decoration: BoxDecoration(
                            color: tradeTypeOption.color(context),
                            borderRadius: BorderRadius.circular(2.gr),
                          ),
                          child: Text(
                            tradeTypeOption.label.tr(),
                            style: context.textTheme.regular.fs9.copyWith(color: Colors.white),
                          ),
                        ),
                        Text(label, style: context.textTheme.regular.fs11.copyWith(color: context.colorTheme.textTitle)),
                      ],
                    ),
                  ),
                );
              },
            ).toList(),
            selected: selectedPosition,
            onChanged: (value) => context.read<TradingCubit>().setSelectedPositionTimed(value),
          ),
          AppDropdown<int>(
            hintText: 'pleaseSelectClosingTime'.tr(),
            selected: isTimerValueSelected ? selectedTimerValue : null,
            items: timerOptions
                .map(
                  (minutes) => DropdownMenuItem<int>(
                    value: minutes,
                    child: Text(
                      '$minutes ${'minutes'.tr()}',
                      style: context.textTheme.regular.fs13.copyWith(color: context.colorTheme.textTitle),
                    ),
                  ),
                )
                .toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  selectedTimerValue = value;
                  isTimerValueSelected = true;
                });
              }
            },
          ),
          CustomMaterialButton(
            isEnabled: (selectedPosition != null && isTimerValueSelected && selectedTimerValue != null),
            height: 30.gh,
            onPressed: () {
              context.read<TradingCubit>().setExpireTime(
                    positionId: selectedPosition!.id!,
                    timerValue: selectedTimerValue!,
                  );
            },
            buttonText: 'submit'.tr(),
            color: context.theme.primaryColor,
            borderColor: context.theme.primaryColor,
            borderRadius: 5.gr,
            textColor: Colors.white,
            fontSize: 13.gr,
          ),
          CustomMaterialButton(
            height: 30.gh,
            onPressed: () => getIt<NavigatorService>().push(AppRouter.routeIndexPage),
            buttonText: 'viewResults'.tr(),
            borderColor: context.colorTheme.border,
            borderRadius: 5.gr,
            textColor: context.colorTheme.textRegular,
            fontSize: 13.gr,
            isOutLined: true,
          )
        ],
      ),
    );
  }
}
