import 'package:animated_flip_counter/animated_flip_counter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/models/entities/trade/trade_handling_fee_config_entity.dart';

import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/domain/models/order/order_response.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/contract/widgets/amount_row.dart';
import 'package:gp_stock_app/features/market/domain/models/create_order_parameter/create_order_parameter.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/models/exchange_rate/exchange_rate.dart';
import 'package:gp_stock_app/shared/widgets/buttons/custom_material_button.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';

class ConfirmDialogWidget extends StatelessWidget {
  const ConfirmDialogWidget({
    super.key,
    required this.tradeType,
    required this.amount,
    required this.directionLabel,
    required this.exchangeRate,
    required this.calculateConfig,
    required this.marketType,
    required this.totalAmount,
    required this.totalAmountUnified,
    required this.selectedPosition,
    required this.isBuySection,
  });
  final int tradeType;
  final String directionLabel;
  final ExchangeRate exchangeRate;
  final MainMarketType marketType;

  final List<TradeHandlingFeeConfigEntity> calculateConfig;
  final double amount;
  final double totalAmount;
  final double totalAmountUnified;
  final OrderRecord? selectedPosition;
  final bool isBuySection;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TradingCubit, TradingState>(
      builder: (context, state) {
        final accountType = state.contract?.label;
        final quantity = state.quantity;
        final limit = state.limit;
        final price = state.priceType.value == 1 ? state.stockInfoConstant?.latestPrice ?? 0 : limit;
        final amount = price * quantity;
        final currency = state.stockInfoConstant?.currency ?? '';
        double fee = context.read<TradingCubit>().calculateFee(amount);
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 16),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  "orderConfirmation".tr(),
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(height: 16),
              BuildRow(
                  label: "transactionDirection".tr(),
                  value: directionLabel,
                  valueColor: Colors.red), // Trade Direction: Buy
              BuildRow(
                  label: "accountType".tr(), value: accountType ?? "spotTrading".tr()), // Account Type: Spot Trading
              BuildRow(
                  label: "orderType".tr(),
                  value:
                      state.priceType.value == 1 ? "marketOrder".tr() : "limitOrder".tr()), // Order Type: Market Order
              BuildRow(
                  label: "price".tr(),
                  value: state.priceType.value == 1
                      ? state.stockInfoConstant?.latestPrice?.toStringAsFixed(3) ?? ''
                      : state.limit.toString()),
              BuildRow(
                label: "quantity".tr(),
                value: "${state.quantity.toStringAsFixed(state.isIndexTrading ? 1 : 0)} ${'lotForSecurities'.tr()}",
              ),
              BuildRow(label: "transactionFee".tr(), value: fee.toStringAsFixed(2), currency: currency),
              BuildRow(label: "orderAmount".tr(), value: amount.toStringAsFixed(2), currency: currency),
              BuildRow(
                label: "totalPrice".tr(),
                value: totalAmount.toStringAsFixed(2),
                currency: currency,
                showTotalToolTip: true,
              ),
              if (marketType != MainMarketType.cnShares && state.contract == null) ...[
                SizedBox(height: 8),
                Row(
                  children: [
                    Spacer(),
                    AnimatedFlipCounter(
                      prefix: '≈ ',
                      duration: const Duration(milliseconds: 500),
                      suffix: ' ${exchangeRate.currencyBase}',
                      thousandSeparator: ',',
                      fractionDigits: 2,
                      textStyle: context.textTheme.primary.w800.copyWith(
                        color: context.theme.primaryColor,
                        fontFamily: 'Akzidenz-Grotesk',
                        height: 1,
                        fontSize: 13.gr,
                      ),
                      value: totalAmountUnified,
                    )
                  ],
                ),
              ],
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  buildButton(
                    context,
                    text: "cancel".tr(),
                    color: context.theme.cardColor,
                    onPressed: () => Navigator.pop(context),
                  ),
                  buildButton(
                    context,
                    text: "submit".tr(),
                    textColor: context.theme.cardColor,
                    onPressed: () {
                      context.read<TradingCubit>().createOrder(
                            CreateOrderParameter(
                              contractAccountId: state.contract?.id,
                              direction: int.parse(state.tradeDirection.value),
                              market: state.stockInfoConstant?.market ?? '',
                              priceType: state.priceType.value,
                              positionId: selectedPosition?.id,
                              securityType: state.stockInfoConstant?.securityType ?? '',
                              symbol: state.stockInfoConstant?.symbol ?? '',
                              symbolName: state.stockInfoConstant?.name ?? '',
                              tradeNum: state.quantity,
                              tradePrice:
                                  state.priceType.value == 1 ? state.stockInfoConstant?.latestPrice ?? 0 : state.limit,
                              tradeType: tradeType,
                              expireType: state.isIndexTrading ? 1 : 2,
                            ),
                            isBuyOrder: isBuySection,
                          );
                      Navigator.pop(context);
                    },
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget buildButton(
    BuildContext context, {
    required String text,
    Color? color,
    Color? textColor,
    required VoidCallback onPressed,
  }) {
    return Expanded(
        child: Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: CustomMaterialButton(
        onPressed: onPressed,
        buttonText: text,
        color: color,
        borderColor: color ?? context.theme.primaryColor,
        borderRadius: 5.gr,
        fontSize: 13.gr,
        textColor: textColor,
        isOutLined: textColor == null,
      ),
    ));
  }
}

class BuildRow extends StatelessWidget {
  final String label;
  final String value;
  final Color? valueColor;
  final String? currency;
  final bool showTotalToolTip;

  const BuildRow({
    super.key,
    required this.label,
    required this.value,
    this.valueColor,
    this.currency,
    this.showTotalToolTip = false,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            spacing: 10,
            children: [
              Text(
                label,
                style: TextStyle(fontSize: 14, color: context.colorTheme.textRegular),
              ),
              if (showTotalToolTip)
                GestureDetector(
                  onTap: () => showToolTip(context),
                  child: Icon(
                    Icons.help_outline,
                    color: context.theme.primaryColor,
                    size: 14.gsp,
                  ),
                ),
            ],
          ),
          if (currency != null)
            AnimatedFlipCounter(
              duration: const Duration(milliseconds: 500),
              suffix: currency == null ? '' : ' $currency',
              thousandSeparator: ',',
              fractionDigits: 2,
              textStyle: context.textTheme.primary.w800.copyWith(
                color: context.theme.primaryColor,
                fontFamily: 'Akzidenz-Grotesk',
                height: 1,
                fontSize: 14,
              ),
              value: double.tryParse(value) ?? 0,
            )
          else
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: valueColor ?? context.theme.primaryColor,
                fontFamily: 'Akzidenz-Grotesk',
              ),
            ),
        ],
      ),
    );
  }
}
