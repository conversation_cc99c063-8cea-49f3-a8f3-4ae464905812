import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/account/logic/account/account_cubit.dart';
import 'package:gp_stock_app/features/account/logic/trading/trading_cubit.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/index_trade/index_radio_section.dart';
import 'package:gp_stock_app/features/account/screens/trading_center/widgets/trade_form/trade_direction_button.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class TradeDirectionSection extends StatelessWidget {
  /// A widget that displays the trade direction section of the trading form.
  ///
  /// This widget handles:
  /// - Display of buy/sell or open/sell buttons based on market type
  /// - Trade direction selection
  /// - Position validation for sell operations
  /// - Integration with index trading mode
  const TradeDirectionSection({
    super.key,
    required this.marketType,
  });

  final MainMarketType marketType;

  @override
  Widget build(BuildContext context) {
    // Select trading mode states
    final state = context.select((TradingCubit cubit) =>
        (isIndexTrading: cubit.state.isIndexTrading, isTimedTrading: cubit.state.isTimedTrading));
    final isIndexTrading = state.isIndexTrading;
    // Get direction labels based on market type and trading mode
    final labels = _getDirectionLabels(state.isIndexTrading);

    // Get and set available quantity from AccountCubit and set maximum quantity to sell in TradingCubit
    final availableQuantityToSell = context.watch<AccountCubit>().availableQuantityToSell();
    context.read<TradingCubit>().setMaximumQuantityToSell(availableQuantityToSell);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Left section: Trade direction label or index radio
        if (isIndexTrading)
          const IndexRadioSection()
        else
          Text(
            'tradeDirection'.tr(),
            style: (switch (AppConfig.instance.skinStyle) {
              AppSkinStyle.kTemplateD => context.textTheme.title,
              _ => context.textTheme.regular,
            }).fs13,
          ),
        // Right section: Trade direction buttons
        if (!(state.isTimedTrading))
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              BlocSelector<TradingCubit, TradingState, TradeDirection>(
                selector: (state) => state.tradeDirection,
                builder: (context, state) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    spacing: 8,
                    children: [
                      TradeDirectionButton(
                        text: labels.buy,
                        color: context.upColor,
                        onPressed: () => context.read<TradingCubit>().toggleTradeDirection(TradeDirection.buy),
                        isSelected: state == TradeDirection.buy,
                      ),
                      TradeDirectionButton(
                        text: labels.sell,
                        color: context.downColor,
                        onPressed: () => _handleSellPress(context, availableQuantityToSell),
                        isSelected: state == TradeDirection.sell,
                      ),
                      if (isIndexTrading)
                        TradeDirectionButton(
                          text: 'transactionDetailsShort'.tr(),
                          color: context.downColor,
                          onPressed: () => getIt<NavigatorService>().push(AppRouter.routeIndexPage,
                            arguments: {
                              'isTransactionDetail': true,
                            },
                          ),
                          isSelected: false,
                        ),
                    ],
                  );
                },
              ),
            ],
          ),
      ],
    );
  }

  /// Determines the appropriate labels for trade direction buttons based on market type and trading mode
  ({String buy, String sell}) _getDirectionLabels(bool isIndexTrading) {
    if (marketType == MainMarketType.cnShares && !isIndexTrading) {
      return (buy: 'buy'.tr(), sell: 'sell'.tr());
    }
    return (buy: 'open'.tr(), sell: 'availableToClose'.tr());
  }

  /// Handles the sell button press with position validation
  void _handleSellPress(BuildContext context, double availableQuantityToSell) {
    // if (availableQuantityToSell <= 0) {
    //   GPEasyLoading.showToast('noAvailablePosition'.tr());
    //   return;
    // }
    context.read<TradingCubit>().toggleTradeDirection(TradeDirection.sell);
  }
}
