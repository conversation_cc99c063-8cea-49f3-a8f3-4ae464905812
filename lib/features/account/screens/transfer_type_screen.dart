import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_lucide/flutter_lucide.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/account/logic/deposit_channel/deposit_channel_state.dart';
import 'package:gp_stock_app/features/account/widgets/table_empty.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:gp_stock_app/shared/widgets/pagination/common_refresher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../domain/models/deposit_channel/deposit_channel_model.dart';
import '../logic/deposit_channel/deposit_channel_cubit.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class TransferTypeScreen extends StatefulWidget {
  const TransferTypeScreen({super.key, this.type = TransferType.deposit});

  final TransferType type;

  @override
  State<TransferTypeScreen> createState() => _TransferTypeScreenState();
}

class _TransferTypeScreenState extends State<TransferTypeScreen> {
  final RefreshController _refreshController = RefreshController();

  @override
  void initState() {
    super.initState();
    if (widget.type == TransferType.deposit) {
      _loadChannels();
    }
  }

  void _loadChannels() {
    context.read<DepositChannelCubit>().getDepositChannels();
  }

  void _onRefresh() async {
    if (widget.type == TransferType.deposit) {
      await context.read<DepositChannelCubit>().getDepositChannels();
    }
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    if (widget.type == TransferType.deposit) {
      final state = context.read<DepositChannelCubit>().state;
      if ((state.channels?.length ?? 0) >= (state.total ?? 0)) {
        _refreshController.loadNoData();
      } else {
        await context.read<DepositChannelCubit>().getDepositChannels(isLoadMore: true);
        _refreshController.loadComplete();
      }
    } else {
      _refreshController.loadNoData();
    }
  }

  @override
  Widget build(BuildContext context) {
    final String screenTitle = widget.type == TransferType.deposit ? 'depositFunds'.tr() : 'withdrawFunds'.tr();

    return Scaffold(
      appBar: AppBar(
        title: Text(screenTitle),
        actions: [
          IconButton(
            icon: Icon(LucideIcons.notepad_text),
            onPressed: () {
              getIt<NavigatorService>().push(widget.type == TransferType.deposit ? AppRouter.routeDepositRecords : AppRouter.routeWithdrawRecords,
              );
            },
          ),
        ],
      ),
      body: widget.type == TransferType.deposit ? _buildDepositContent() : _buildWithdrawContent(),
    );
  }

  Widget _buildDepositContent() {
    return BlocBuilder<DepositChannelCubit, DepositChannelState>(
      builder: (context, state) {
        if (state.channelsStatus == DataStatus.loading && (state.channels?.isEmpty ?? true)) {
          return const Center(child: CircularProgressIndicator.adaptive());
        }

        if (state.channelsStatus == DataStatus.failed || (state.channels?.isEmpty ?? true)) {
          return CommonRefresher(
            controller: _refreshController,
            enablePullDown: true,
            onRefresh: _onRefresh,
            child: ListView(
              physics: const AlwaysScrollableScrollPhysics(),
              children: [
                SizedBox(
                  height: 0.7.gsh,
                  child: Center(
                    child: TableEmptyWidget(
                      width: 65.gw,
                      height: 65.gh,
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return CommonRefresher(
          controller: _refreshController,
          enablePullDown: true,
          enablePullUp: true,
          onRefresh: _onRefresh,
          onLoading: _onLoading,
          child: ListView.builder(
            padding: EdgeInsets.all(16.gr),
            itemCount: state.channels?.length ?? 0,
            itemBuilder: (context, index) {
              final channel = state.channels![index];
              return Padding(
                padding: EdgeInsets.only(bottom: 16.gh),
                child: _buildChannelCard(context, channel),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildWithdrawContent() {
    // For withdrawal, we're using a hard-coded item as requested
    return Padding(
      padding: EdgeInsets.all(16.gr),
      child: Column(
        children: [
          InkWell(
            onTap: () => getIt<NavigatorService>().push(AppRouter.routeWithdraw),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gh),
              decoration: BoxDecoration(
                color: context.colorTheme.stockRed,
                borderRadius: BorderRadius.circular(8.gr),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 22.gr,
                    backgroundColor: Colors.white,
                    child: CircleAvatar(
                      radius: 15.gr,
                      backgroundColor: context.colorTheme.stockRed,
                      child: Text(
                        'CNY',
                        style: TextStyle(fontSize: 12.gr, color: Colors.white),
                      ),
                    ),
                  ),
                  SizedBox(width: 8.gw),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '人民币',
                        style: context.textTheme.primary.fs16.w600.copyWith(color: Colors.white),
                      ),
                      SizedBox(height: 4.gh),
                      Text(
                        '人民币提现通道',
                        style: context.textTheme.regular.fs12.copyWith(color: Colors.white),
                      ),
                    ],
                  ),
                  Spacer(),
                  Text(
                    'CNY',
                    style: context.textTheme.primary.w700.copyWith(
                      fontSize: 30,
                      color: Colors.white.withValues(alpha: 0.3),
                    ),
                  ),
                  SizedBox(width: 4.gw),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.gr,
                    color: Colors.white.withValues(alpha: 0.3),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChannelCard(BuildContext context, DepositChannelModel channel) {
    final subtitle = '存款金额 ${channel.minAmount?.toStringAsFixed(0)}~${channel.maxAmount?.toStringAsFixed(0)}';

    return InkWell(
      onTap: () => getIt<NavigatorService>().push(AppRouter.routeDeposit, arguments: channel),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.gw, vertical: 12.gh),
        decoration: BoxDecoration(
          color: context.colorTheme.stockRed,
          borderRadius: BorderRadius.circular(8.gr),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  channel.channelName ?? '',
                  style: context.textTheme.primary.fs16.w600.copyWith(color: Colors.white),
                ),
                SizedBox(height: 4.gh),
                Text(
                  subtitle,
                  style: context.textTheme.regular.fs12.copyWith(color: Colors.white),
                ),
              ],
            ),
            Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 16.gr,
              color: Colors.white.withValues(alpha: 0.3),
            ),
          ],
        ),
      ),
    );
  }
}
