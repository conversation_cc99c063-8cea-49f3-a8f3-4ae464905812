import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';

import '../../../../core/api/network/endpoint/api_endpoints.dart';
import '../../../../core/api/network/models/result.dart';
import '../../../../core/api/network/network.dart';
import '../models/account_info/account_info_response.dart';
import '../models/account_summary/contract_summary_response.dart';
import '../models/order/order_response.dart';
import '../repository/account_repository.dart';

@Injectable(as: AccountRepository)
class AccountService implements AccountRepository {
  @override
  Future<ResponseResult<AccountInfoResponse>> getAccountInfo() async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getAccountInfo,
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: AccountInfoResponse.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get account info');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<ContractSummaryData>> getCurrentContractSummary(int contractId) async {
    try {
      final Response response = await NetworkProvider().get(
        '${ApiEndpoints.getContractSummary}/$contractId',
        isAuthRequired: true,
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: ContractSummaryData.fromJson(response.data['data']),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get contract summary');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<ContractSummaryResponse>> getContractSummary({int page = 1, int? settlementStatus}) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getContractSummaryPage,
        isAuthRequired: true,
        force: true,
        queryParameters: {
          'settlementStatus': settlementStatus,
          'pageNumber': page,
          'pageSize': 20,
        },
      );
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(
            data: ContractSummaryResponse.fromJson(response.data),
          );
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get contract summary');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<OrderResponse>> getOrderList({
    int page = 1,
    int? pageSize = 20,
    int? status, // 订单委托状态： 0 委托中 1委托撤销 2.订单成交成功 3.合约到期自动撤销
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType, // 	数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
  }) async {

    /// https://h5.gpnow.xyz/doc/doc.html#/%E4%BC%9A%E5%91%98%E7%AB%AF/app%E7%AB%AF-%E8%AE%A2%E5%8D%95%E7%9B%B8%E5%85%B3/pageUsingGET_11
    try {
      final Response response =
          await NetworkProvider().get(ApiEndpoints.getOrderList, isAuthRequired: true, queryParameters: {
        'pageNum': page,
        'pageSize': pageSize,
        if (dataType != null) 'dataType': dataType,
        if (contractId != null) 'contractId': contractId,
        if (status != null) 'status': status,
        if (symbol != null) 'symbol': symbol,
        if (market != null) 'market': market,
        if (securityType != null) 'securityType': securityType,
        if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
      });
      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: OrderResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get order list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<OrderRecord>> getOrderById({
    required int id,
  }) async {
    try {
      final Response response = await NetworkProvider().get(
        ApiEndpoints.getOrderById,
        isAuthRequired: true,
        queryParameters: {
          'id': id,
        },
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: OrderRecord.fromJson(response.data['data']));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get order details');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  @override
  Future<ResponseResult<OrderResponse>> getPositionList({
    int page = 1,
    int? pageSize = 20,
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType, // 	数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货
  }) async {
    try {
      final Response response = await NetworkProvider().get(ApiEndpoints.getPositionList,
          isAuthRequired: true,
          queryParameters: {
            'pageNumber': page,
            'pageSize': pageSize,

            if (dataType != null) 'dataType': dataType,
            if (contractId != null) 'contractId': contractId,
            if (symbol != null) 'symbol': symbol,
            if (market != null) 'market': market,
            if (securityType != null) 'securityType': securityType,
            if (commentAssetId != null && contractId == null) 'commentAssetId': commentAssetId,
          });

      if (response.statusCode == 200 || response.statusCode == 201) {
        if (response.data['code'] == 0) {
          return ResponseResult(data: OrderResponse.fromJson(response.data));
        } else {
          return ResponseResult(error: response.data['msg']);
        }
      } else {
        return ResponseResult(error: 'Failed to get position list');
      }
    } on DioException catch (e) {
      return ResponseResult(error: e.error.toString());
    }
  }

  /// 成交历史查询
  @override
  Future<ResponseResult<OrderResponse>> getOrderHistoryTransaction({
    int page = 1,
    int? contractId,
    String? commentAssetId,
    int? status,
  }) async {
    return getOrderList(
      page: page,
      status: status,
      contractId: contractId,
      commentAssetId: commentAssetId,
    );
    // try {
    //   final Response response = await NetworkProvider().get(
    //     ApiEndpoints.getOrderList,
    //     queryParameters: {'pageNum': page, 'pageSize': 20, 'status': status}
    //       ..addAll(commentAssetId != null && contractId == null ? {'commentAssetId': commentAssetId} : {})
    //       ..addAll(contractId != null ? {'contractId': contractId} : {}),
    //     isAuthRequired: true,
    //   );
    //   if (response.statusCode == 200 || response.statusCode == 201) {
    //     if (response.data['code'] == 0) {
    //       return ResponseResult(data: OrderResponse.fromJson(response.data));
    //     } else {
    //       return ResponseResult(error: response.data['msg']);
    //     }
    //   } else {
    //     return ResponseResult(error: 'Failed to get order history');
    //   }
    // } on DioException catch (e) {
    //   return ResponseResult(error: e.error.toString());
    // }
  }

  /// 佣金记录查询（只传 contractId/commentAssetId）
  @override
  Future<ResponseResult<OrderResponse>> getOrderHistoryCommission({
    int page = 1,
    int? contractId,
    String? commentAssetId,
  }) async {
    return getOrderList(
      page: page,
      contractId: contractId,
      commentAssetId: commentAssetId,
    );
    // try {
    //   final Response response = await NetworkProvider().get(
    //     ApiEndpoints.getOrderList,
    //     queryParameters: {'pageNum': page, 'pageSize': 20}
    //       ..addAll(contractId != null ? {'contractId': contractId} : {})
    //       ..addAll(commentAssetId != null && contractId == null ? {'commentAssetId': commentAssetId} : {}),
    //     isAuthRequired: true,
    //   );
    //   if (response.statusCode == 200 || response.statusCode == 201) {
    //     if (response.data['code'] == 0) {
    //       return ResponseResult(data: OrderResponse.fromJson(response.data));
    //     } else {
    //       return ResponseResult(error: response.data['msg']);
    //     }
    //   } else {
    //     return ResponseResult(error: 'Failed to get order history');
    //   }
    // } on DioException catch (e) {
    //   return ResponseResult(error: e.error.toString());
    // }
  }


}
