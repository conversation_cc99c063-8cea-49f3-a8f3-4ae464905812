import 'package:gp_stock_app/core/api/network/models/result.dart';

import '../models/account_info/account_info_response.dart';
import '../models/account_summary/contract_summary_response.dart';
import '../models/order/order_response.dart';

abstract class AccountRepository {
  Future<ResponseResult<AccountInfoResponse>> getAccountInfo();
  Future<ResponseResult<ContractSummaryResponse>> getContractSummary({int page, int? settlementStatus});
  Future<ResponseResult<ContractSummaryData>> getCurrentContractSummary(int contractId);
  Future<ResponseResult<OrderResponse>> getOrderList({
    int page = 1,
    int? pageSize,
    int? status,
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType,
  });

  Future<ResponseResult<OrderRecord>> getOrderById({
    required int id,
  });
  Future<ResponseResult<OrderResponse>> getPositionList({
    int page,
    int? pageSize,
    int? contractId,
    String? symbol,
    String? market,
    String? securityType,
    String? commentAssetId,
    int? dataType,
  });
  Future<ResponseResult<OrderResponse>> getOrderHistoryTransaction({
    int page,
    int? contractId,
    String? commentAssetId,
    int? status,
  });
  Future<ResponseResult<OrderResponse>> getOrderHistoryCommission({
    int page,
    int? contractId,
    String? commentAssetId,
  });

}
