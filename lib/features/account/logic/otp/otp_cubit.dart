import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/models/apis/auth.dart';
import 'package:gp_stock_app/core/utils/wangyi_captcha_util.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

import 'otp_state.dart';

@injectable
class OtpCubit extends Cubit<OtpState> {
  OtpCubit() : super(const OtpState());

  Future<void> verifyAndSendOtp(String mobile, {OtpType type = OtpType.bindBankCard}) async {
    final (bool isSuccess, bool isNeedCaptcha) = await AuthApi.fetchWangYiCaptchaRequired(
      type: WangYiCaptchaType.kSms,
    );
    if (!isSuccess) {
      return GPEasyLoading.showToast("get_verification_method_failed".tr());
    }
    if (isNeedCaptcha) {
      _handleCaptcha(mobile, type: type);
    } else {
      sendOtp(mobile, type: type);
    }
  }

  void _handleCaptcha(String mobile, {OtpType type = OtpType.bindBankCard}) {
    WangYiCaptchaUtil().show(
      captchaId: AppConfig.instance.wangYiCaptchaKey,
      account: mobile,
      onSuccess: (code) => sendOtp(mobile, type: type, validate: code),
      onValidateFailClose: () {
        if (!isClosed) {
          emit(state.copyWith(sendStatus: DataStatus.idle));
        }
      },
      onError: () {
        if (!isClosed) {
          GPEasyLoading.showToast('验证失败'.tr());
          emit(state.copyWith(sendStatus: DataStatus.failed));
        }
      },
    );
  }

  Future<void> sendOtp(String mobile, {OtpType type = OtpType.bindBankCard, String? validate}) async {
    if (state.sendStatus == DataStatus.loading || state.isTimerActive) return;

    emit(state.copyWith(sendStatus: DataStatus.loading, isSent: false, timerDuration: 60));

    final flag = await AuthApi.fetchOTP(phoneNumber: mobile, sendType: type.text, validate: validate);
    if (flag) {
      emit(state.copyWith(sendStatus: DataStatus.success, isSent: true, isTimerActive: true));
      _startTimer();
    } else {
      emit(state.copyWith(sendStatus: DataStatus.failed, isSent: false));
    }
  }

  void _startTimer() {
    // Cancel any existing timer
    if (state.timer != null) {
      state.timer!.cancel();
      emit(state.copyWith(timer: null)); // Clear the timer using copyWith
    }

    const oneSec = Duration(seconds: 1);
    var remainingDuration = state.timerDuration;
    final timer = Timer.periodic(oneSec, (timer) {
      if (remainingDuration == 0) {
        timer.cancel();
        emit(state.copyWith(
          sendStatus: DataStatus.success,
          isSent: true,
          timerDuration: 0,
          isTimerActive: false,
          timer: null,
        ));
      } else {
        remainingDuration--;
        if (isClosed) return;
        emit(state.copyWith(timerDuration: remainingDuration, isTimerActive: true));
      }
    });

    // Set the new timer using copyWith
    emit(state.copyWith(timer: timer));
  }

  void reset() {
    if (state.timer != null) {
      state.timer!.cancel();
    }
    emit(const OtpState(sendStatus: DataStatus.idle));
  }
}
