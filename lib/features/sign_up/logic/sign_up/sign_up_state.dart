part of 'sign_up_cubit.dart';

class SignUpState extends Equatable {
  final DataStatus signUpFetchStatus;
  final String? error;

  const SignUpState({
    this.signUpFetchStatus = DataStatus.idle,
    this.error,
  });

  SignUpState copyWith({
    String? inviteCode,
    DataStatus? signUpFetchStatus,
    String? error,
  }) {
    return SignUpState(
      signUpFetchStatus: signUpFetchStatus ?? this.signUpFetchStatus,
      error: error ?? this.error,
    );
  }

  @override
  List<Object?> get props => [
        signUpFetchStatus,
        error,
      ];
}
