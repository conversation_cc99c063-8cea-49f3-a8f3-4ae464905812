import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'trading_center_v2_cubit.dart';

class TradingCenterV2Page extends StatelessWidget {
  const TradingCenterV2Page({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => TradingCenterV2Cubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<TradingCenterV2Cubit>(context);

    return Container();
  }
}


