import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'market_detail_cubit.dart';

class MarketDetailPage extends StatelessWidget {
  const MarketDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => MarketDetailCubit(),
      child: Builder(builder: (context) => _buildPage(context)),
    );
  }

  Widget _buildPage(BuildContext context) {
    final cubit = BlocProvider.of<MarketDetailCubit>(context);

    return Container();
  }
}


