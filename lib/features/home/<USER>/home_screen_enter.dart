import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/home_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/style_a_home_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/style_c_home_screen.dart';
import 'package:gp_stock_app/features/home/<USER>/style_d_home_screen.dart';
import 'package:gp_stock_app/features/market/logic/market/market_cubit.dart';

class HomeScreenEnter extends StatelessWidget {
  const HomeScreenEnter({super.key});

  @override
  Widget build(BuildContext context) {
    final errorWidget = Container(width: 1.gsw, height: 1.gsh, alignment: Alignment.center, child: Text("HomeScreenEnter未配置"),);
    return BlocProvider(
      create: (context) => getIt<MarketCubit>()..init(),
      child: Builder(builder: (context) {
        switch (AppConfig.instance.skinStyle) {
          case AppSkinStyle.kGP:
            switch (AppConfig.instance.colorSchemeStyle) {
              case ColorSchemeStyle.kDefault:
                return HomeScreen();
              case ColorSchemeStyle.kGolden:
              case ColorSchemeStyle.kOrange:
                return errorWidget;
            }
          case AppSkinStyle.kTemplateA:
            switch (AppConfig.instance.colorSchemeStyle) {
              case ColorSchemeStyle.kDefault:
                return StyleAHomeScreen();
              case ColorSchemeStyle.kGolden:
              case ColorSchemeStyle.kOrange:
                return errorWidget;
            }

          case AppSkinStyle.kTemplateB:
            return errorWidget;
          case AppSkinStyle.kTemplateC:
            return StyleCHomeScreen();

          case AppSkinStyle.kTemplateD:
            switch (AppConfig.instance.colorSchemeStyle) {
              case ColorSchemeStyle.kDefault:
                return StyleDHomeScreen();
              case ColorSchemeStyle.kGolden:
              case ColorSchemeStyle.kOrange:
                return errorWidget;
            }
        }
      }),
    );
  }
}
