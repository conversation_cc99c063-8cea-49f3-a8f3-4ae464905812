import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../shared/app/utilities/easy_loading.dart';
import '../../../shared/constants/assets.dart';
import '../../../shared/logic/sys_settings/sys_settings_cubit.dart';
import '../../../shared/logic/theme/theme_cubit.dart';
import '../widgets/home_menu_button.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class HomeMenu extends StatefulWidget {
  const HomeMenu({super.key});

  @override
  State<HomeMenu> createState() => _HomeMenuState();
}

class _HomeMenuState extends State<HomeMenu> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  late final List<Animation<double>> _animations;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _animations = List.generate(
      3,
      (index) => CurvedAnimation(
        parent: _controller,
        curve: Interval(
          index * 0.2,
          0.6 + index * 0.2,
          curve: Curves.easeOut,
        ),
      ),
    );

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _getThemeAwareIcon(BuildContext context, ThemeMode themeMode, String lightIcon, String darkIcon) {
    if (themeMode == ThemeMode.system) {
      return MediaQuery.of(context).platformBrightness == Brightness.light ? lightIcon : darkIcon;
    } else {
      return themeMode == ThemeMode.light ? lightIcon : darkIcon;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.gw),
      child: BlocSelector<ThemeCubit, ThemeState, ThemeMode>(
          selector: (state) => state.themeMode,
          builder: (context, themeMode) {
            return Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                FadeTransition(
                  opacity: _animations[0],
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(-0.5, 0),
                      end: Offset.zero,
                    ).animate(_animations[0]),
                    child: HomeMenuButton(
                      icon: _getThemeAwareIcon(context, themeMode, Assets.homeH5Icon, Assets.homeH5IconDark),
                      title: 'homeH5Title',
                      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeAboutUs)),
                    ),
                  ),
                ),
                5.horizontalSpace,
                FadeTransition(
                  opacity: _animations[1],
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(-0.5, 0),
                      end: Offset.zero,
                    ).animate(_animations[1]),
                    child: HomeMenuButton(
                      icon: _getThemeAwareIcon(context, themeMode, Assets.homeH2Icon, Assets.homeH2IconDark),
                      title: 'aiAnalysis',
                      onTap: () => AuthUtils.verifyAuth(() => getIt<NavigatorService>().push(AppRouter.routeAIChat)),
                    ),
                  ),
                ),
                5.horizontalSpace,
                FadeTransition(
                  opacity: _animations[2],
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(-0.5, 0),
                      end: Offset.zero,
                    ).animate(_animations[2]),
                    child: HomeMenuButton(
                      icon: _getThemeAwareIcon(context, themeMode, Assets.homeH4Icon, Assets.homeH4IconDark),
                      title: 'homeH4Title',
                      onTap: () {
                        // Verify authentication before opening the service URL
                        AuthUtils.verifyAuth(() {
                          // Get the service URL from system settings
                          final state = context.read<SysSettingsCubit>().state;

                          // Use pattern matching with maybeWhen to handle different states
                          state.maybeWhen(
                            loaded: (sysSettings, _) {
                              final serviceUrl = sysSettings.service;
                              if (serviceUrl != null && serviceUrl.isNotEmpty) {
                                launchUrl(Uri.parse(serviceUrl), mode: LaunchMode.inAppBrowserView);
                              } else {
                                GPEasyLoading.showToast('something_went_wrong'.tr());
                              }
                            },
                            orElse: () {
                              // Fallback if system settings are not loaded
                              GPEasyLoading.showToast('something_went_wrong'.tr());
                            },
                          );
                        });
                      },
                    ),
                  ),
                ),
              ],
            );
          }),
    );
  }
}
