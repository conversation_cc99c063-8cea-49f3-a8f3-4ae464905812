import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/models/home_notification/home_notification_model.dart';
import 'package:gp_stock_app/features/home/<USER>/home_notification_cubit/home_notification_cubit.dart';
import 'package:gp_stock_app/features/home/<USER>/home_marquee_text.dart';
import 'package:gp_stock_app/shared/app/extension/string_extension.dart';
import 'package:gp_stock_app/shared/widgets/text/common_marquee_text.dart';
import 'package:html/parser.dart' as html_parser;

class StyleAHomeMarqueeText extends StatefulWidget {
  const StyleAHomeMarqueeText({super.key});

  @override
  State<StyleAHomeMarqueeText> createState() => _StyleAHomeMarqueeTextState();
}

class _StyleAHomeMarqueeTextState extends State<StyleAHomeMarqueeText> {
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<HomeNotificationCubit, List<HomeNotificationModel>>(
      listener: (context, state) {
        if (context.read<HomeNotificationCubit>().shouldSuppressPopup) {
          return;
        }

        final notifications = state.where((e) => e.type == 3).toList();
        if (notifications.isNotEmpty) {
          // Preload images before showing the dialog
          for (var notification in notifications) {
            if (notification.imageUrl.isNullOrEmpty) continue;
            precacheImage(
              CachedNetworkImageProvider(notification.imageUrl),
              context,
            );
          }
          WidgetsBinding.instance.addPostFrameCallback((_) {
            showDialog(
              context: context,
              builder: (context) => NotificationPopup(
                notifications: notifications,
              ),
            );
          });
        }
      },
      builder: (context, state) {
        return _buildMarqueeContainer(context, state);
      },
    );
  }

  Widget _buildMarqueeContainer(BuildContext context, List<HomeNotificationModel> state) {
    // Get notifications of type 2
    final typeNotifications = state.where((e) => e.type == 2).toList();

    // If there are no notifications, don't show the container at all
    if (typeNotifications.isEmpty) {
      return const SizedBox.shrink(); // Return an empty widget
    }

    return Container(
      height: 54.gw,
      margin: EdgeInsets.only(top: 5.gw),
      padding: EdgeInsets.symmetric(horizontal: 14.gw),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [context.colorTheme.buttonPrimary, context.theme.primaryColor],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
        borderRadius: BorderRadius.circular(4.gw),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0F354677),
            offset: Offset(0, 4),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 1.gw),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            IconHelper.loadAsset(
              'assets/svg/marquee.svg',
              width: 28,
              height: 28,
            ),
            SizedBox(width: 6.gw),
            Expanded(
              child: Padding(
                padding: EdgeInsets.only(top: 2.gw),
                child: _buildMarqueeText(context, typeNotifications),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Builds the marquee text widget with proper error handling
  /// The notifications passed here are already filtered for type 2
  Widget _buildMarqueeText(BuildContext context, List<HomeNotificationModel> notifications) {

    return CommonMarqueeText(
        items: notifications
            .map((e) => TextSpan(
                  text: html_parser.parse(e.content).body?.text ?? e.content,
                  style: context.textTheme.primary.copyWith(color: Color(0xffC92C31)),
                ))
            .toList());

  }
}
