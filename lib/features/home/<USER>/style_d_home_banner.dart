import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/features/home/<USER>/models/banner/banner_response.dart';
import 'package:gp_stock_app/features/home/<USER>/home/<USER>';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/widgets/shimmer/shimmer_widget.dart';

class StyleDHomeBanner extends StatefulWidget {
  final DataStatus dataStatus;
  final List<BannerData> list;
  final Function(int) onPressed;
  final Color? color;

  const StyleDHomeBanner({
    super.key,
    required this.dataStatus,
    required this.list,
    required this.onPressed,
    this.color,
  });

  @override
  State<StyleDHomeBanner> createState() => _StyleDHomeBannerState();
}

class _StyleDHomeBannerState extends State<StyleDHomeBanner> {
  final CarouselSliderController carouselController = CarouselSliderController();
  int currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double bannerHeight = (screenWidth - 20) * 126 / 355;

    if (widget.dataStatus == DataStatus.loading) {
      return ShimmerWidget(
        width: double.infinity,
        height: bannerHeight,
        radius: 4,
      );
    }

    // Handle null or empty list
    if (widget.list.isEmpty) {
      return SizedBox(
        width: double.infinity,
      );
    }

    return Container(
      color: widget.color ?? context.theme.appBarTheme.backgroundColor,
      padding: const EdgeInsets.only(top: 10, bottom: 5),
      child: Column(
        children: [
          CarouselSlider.builder(
            options: CarouselOptions(
              aspectRatio: 348 / 126,
              viewportFraction: 1,
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 5),
              enableInfiniteScroll: true,
              onPageChanged: (index, reason) {
                setState(() => currentIndex = index);
                context.read<HomeCubit>().updateBannerIndex(index);
              },
            ),
            itemCount: widget.list.length,
            itemBuilder: (BuildContext context, int itemIndex, int pageViewIndex) => InkWell(
              onTap: () => widget.onPressed(itemIndex),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                width: double.infinity,
                height: bannerHeight,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  image: DecorationImage(
                    image: CachedNetworkImageProvider(widget.list[itemIndex].imageUrl ?? ''),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            carouselController: carouselController,
          ),
          6.verticalSpace,
          DotsIndicator(
            dotsCount: widget.list.length,
            position: currentIndex.toDouble(),
            decorator: DotsDecorator(
              size: const Size.square(9.0),
              activeSize: const Size(23.0, 7.0),
              color: context.colorTheme.textRegular,
              activeColor: context.theme.primaryColor,
              activeShape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5.0)),
            ),
          )
        ],
      ),
    );
  }
}
