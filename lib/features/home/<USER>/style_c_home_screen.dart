import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/app_navigation_handler.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/features/home/<USER>/style_d_home_banner.dart';
import 'package:gp_stock_app/features/home/<USER>/style_d_home_market_tab.dart';
import 'package:gp_stock_app/features/home/<USER>/style_d_home_marquee_text.dart';
import 'package:gp_stock_app/features/home/<USER>/style_d_home_menu.dart';
import 'package:gp_stock_app/features/market/logic/cubit/index_trade_cubit.dart';

import '../../account/logic/account/account_cubit.dart';
import '../../activity/logic/activity/activity_cubit.dart';
import '../../market/logic/market/market_cubit.dart';
import '../../notifications/logic/notifications/notifications_cubit.dart';
import '../logic/home/<USER>';
import '../logic/news/news_cubit.dart';
import '../widgets/news_and_events.dart';

class StyleCHomeScreen extends StatefulWidget {
  const StyleCHomeScreen({super.key});

  @override
  State<StyleCHomeScreen> createState() => _StyleCHomeScreenState();
}

class _StyleCHomeScreenState extends State<StyleCHomeScreen> with WidgetsBindingObserver {
  int _appLifecycleStatePausedTimeSecond = 0;

  @override
  void initState() {
    super.initState();
    getIt<IndexTradeCubit>().subscribeToTimeline();
    WidgetsBinding.instance.addObserver(this);
  }

  Future<void> _initialFunction() async {
    final currentContext = context;
    if (!currentContext.mounted) return;
    context.read<HomeCubit>().getBannerList();
    context.read<AccountCubit>().getContractSummary();
    context.read<MarketCubit>().fetchTableData(sortType: 1, order: 'DESC', isHome: true);
    context.read<NotificationsCubit>().getNotificationCount();
    context.read<MarketCubit>().fetchTableData(sortType: 0, order: 'DESC', isHome: true);
    context.read<ActivityCubit>().getTasks();
    context.read<NewsCubit>().getNews();
  }

  @override
  void dispose() {
    getIt<IndexTradeCubit>().unsubscribeFromTimeline();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    int now = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    if (state == AppLifecycleState.resumed && (now - _appLifecycleStatePausedTimeSecond) > 30) {
      getIt<IndexTradeCubit>().reloadTimeline();
    } else if (state == AppLifecycleState.paused) {
      _appLifecycleStatePausedTimeSecond = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator.adaptive(
        onRefresh: () async => await _initialFunction(),
        child: AnimationLimiter(
          child: ListView(
            children: AnimationConfiguration.toStaggeredList(
              duration: const Duration(milliseconds: 300),
              childAnimationBuilder: (widget) => SlideAnimation(
                verticalOffset: 50.0,
                child: FadeInAnimation(
                  child: widget,
                ),
              ),
              children: [
                Stack(
                  children: [
                    ClipPath(
                      clipper: _BottomArcClipper(),
                      child: Container(
                        height: 100, // 50(矩形) + 50(椭圆)
                        decoration: BoxDecoration(
                          color: context.theme.appBarTheme.backgroundColor,
                        ),
                      ),
                    ),
                    BlocBuilder<HomeCubit, HomeState>(builder: (context, state) {
                      return StyleDHomeBanner(
                        color: Colors.transparent,
                        dataStatus: state.bannerFetchStatus,
                        list: state.bannerData ?? [],
                        onPressed: (idx) {
                          final banner = state.bannerData![idx];
                          AppNavigationHandler.handleNavigation(context,
                              jumpType: banner.jumpType, jumpUrl: banner.jumpUrl);
                        },
                      );
                    }),
                  ],
                ),
                StyleDHomeMenu(
                  color: Colors.transparent,
                  isOriginalColor: true,
                  showVip: true,
                ),
                _buildShadowBox(child: StyleDHomeMarqueeText()),
                12.verticalSpace,
                _buildShadowBox(child: StyleDHomeMarketTab()),
                12.verticalSpace,
                _buildShadowBox(child: NewsAndEvents()),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShadowBox({required Widget child}) {
    return Container(
      clipBehavior: Clip.hardEdge,
      margin: EdgeInsets.symmetric(horizontal: 10.gw),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.gw), boxShadow: [
        BoxShadow(
          color: context.theme.shadowColor, // #35467705 转换为 ARGB
          offset: Offset(0, 4), // 0px 4px
          blurRadius: 4, // 模糊半径
          spreadRadius: 0, // 0px
        ),
      ]),
      child: child,
    );
  }
}


/// 顶部半圆背景
class _BottomArcClipper extends CustomClipper<Path> {
  @override
    Path getClip(Size size) {
    final path = Path();
    
    // 绘制100高度的矩形部分
    path.moveTo(0, 0); // 左上角
    path.lineTo(size.width, 0); // 右上角
    path.lineTo(size.width, 50); // 右边到矩形底部
    
    // 绘制50高度的半椭圆部分
    path.quadraticBezierTo(
      size.width / 2, 100, // 控制点：100(50+50)
      0, 50, // 终点：左边矩形底部
    );
    
    // 闭合路径
    path.close();

    return path;
  }


  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}