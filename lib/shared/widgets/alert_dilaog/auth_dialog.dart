import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';

import '../../routes/app_router.dart';

import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/shared/routes/navigator_utils.dart';

class AuthRequiredDialog extends StatelessWidget {
  const AuthRequiredDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.gr),
      ),
      child: Container(
        padding: EdgeInsets.all(20.gr),
        decoration: BoxDecoration(
          color: context.theme.cardColor,
          borderRadius: BorderRadius.circular(16.gr),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(12.gr),
              decoration: BoxDecoration(
                color: context.theme.primaryColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.lock_outline,
                color: context.theme.primaryColor,
                size: 20.gr,
              ),
            ),
            16.verticalSpace,
            Text(
              'loginRequired'.tr(),
              style: context.textTheme.primary.fs16.w600,
            ),
            12.verticalSpace,
            Text(
              'pleaseLoginToContinue'.tr(),
              textAlign: TextAlign.center,
              style: context.textTheme.regular.fs12,
            ),
            24.verticalSpace,
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 8.gh),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.gr),
                      ),
                    ),
                    child: Text(
                      'cancel'.tr(),
                      style: context.textTheme.regular,
                    ),
                  ),
                ),
                12.horizontalSpace,
                Expanded(
                  child: ElevatedButton(
                    onPressed: () =>
                        getIt<NavigatorService>().push(AppRouter.routeLogin, arguments: {'needRedirection': true}).then((value) {
                      if (!context.mounted) return;
                      Navigator.pop(context, value);
                    }),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.theme.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(vertical: 8.gh),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.gr),
                      ),
                    ),
                    child: Text(
                      'login'.tr(),
                      style: context.textTheme.secondary.w500,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
