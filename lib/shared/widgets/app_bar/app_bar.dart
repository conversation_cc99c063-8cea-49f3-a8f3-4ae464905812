import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/auth/auth_utils.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/icon_helper.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/core/utils/utils.dart';
import 'package:gp_stock_app/features/notifications/logic/notifications/notifications_cubit.dart';
import 'package:gp_stock_app/shared/logic/sys_settings/sys_settings_cubit.dart';

import '../../../core/dependency_injection/injectable.dart';
import '../../../features/home/<USER>/settings_menu.dart';
import '../../../features/market/logic/search/search_cubit.dart';
import '../../../features/market/market_search_screen.dart';
import '../../constants/assets.dart';
import '../../logic/theme/theme_cubit.dart';
import '../text_fields/text_field_widget.dart';

class MainTitle extends StatelessWidget {
  const MainTitle({super.key});

  void _showSettingsMenu(BuildContext context) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final buttonPosition = button.localToGlobal(Offset.zero);
    final RelativeRect position = RelativeRect.fromSize(
      Rect.fromLTRB(buttonPosition.dx + button.size.width - 200.gw, buttonPosition.dy + 50.gh,
          buttonPosition.dx + button.size.width, buttonPosition.dy + 100.gh),
      Size(110, 160),
    );

    showMenu(
      context: context,
      position: position,
      elevation: 0,
      color: Colors.transparent,
      constraints: BoxConstraints(maxWidth: 200.gw),
      items: [
        PopupMenuItem(
          enabled: false,
          padding: EdgeInsets.zero,
          child: SettingsMenu(),
        ),
      ],
    );
  }

  @override
  AppBar build(BuildContext context) {
    final sysSettings = context.read<SysSettingsCubit>().state.maybeWhen(
          loaded: (sysSettings, _) => sysSettings,
          orElse: () => null,
        );
    final imageUrl = (isDarkMode(context)
        ? (sysSettings?.logoDark ?? Assets.appLogoTitleDark)
        : (sysSettings?.logoLight ?? Assets.appLogoTitle));

    return AppBar(
      surfaceTintColor: Colors.transparent,
      backgroundColor: context.theme.appBarTheme.backgroundColor,
      elevation: 0,
      title: Row(
        children: [
          BlocSelector<ThemeCubit, ThemeState, ThemeMode>(
            selector: (state) => state.themeMode,
            builder: (context, themeMode) {
              return CachedNetworkImage(
                imageUrl: imageUrl,
                errorWidget: (context, error, stackTrace) => Image.asset(
                  themeMode == ThemeMode.light ? Assets.appLogoTitle : Assets.appLogoTitleDark,
                  fit: BoxFit.scaleDown,
                  width: 100.gw,
                  height: 32.gh,
                ),
                fit: BoxFit.scaleDown,
                width: 100.gw,
                height: 32.gh,
              );
            },
          ),
          9.horizontalSpace,
          Expanded(
            child: SizedBox(
              child: GestureDetector(
                onTap: () => AuthUtils.verifyAuth(
                  () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BlocProvider(
                        create: (context) => getIt<SearchCubit>(),
                        child: const MarketSearchScreen(),
                      ),
                    ),
                  ),
                ),
                child: TextFieldWidget(
                  enabled: false,
                  constraints: BoxConstraints(maxHeight: 30.gh),
                  hintText: 'searchPlaceholder'.tr(),
                  borderRadius: 30,
                  prefixIcon: SvgPicture.asset(
                    Assets.searchIcon,
                    fit: BoxFit.scaleDown,
                    width: 24.gw,
                    height: 24.gh,
                  ),
                  fillColor: context.theme.cardColor,
                ),
              ),
            ),
          ),
          12.horizontalSpace,
          InkWell(
            onTap: () => _showSettingsMenu(context),
            child: BlocSelector<NotificationsCubit, NotificationsState, int?>(
              selector: (state) => state.notificationCount,
              builder: (context, notificationCount) {
                return Badge(
                  offset: const Offset(10, -10),
                  isLabelVisible: notificationCount != null && notificationCount > 0,
                  child: IconHelper.loadAsset(
                    Assets.menuIcon,
                    fit: BoxFit.scaleDown,
                    color: getIconColor(context),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Color? getIconColor(BuildContext context) {
    return switch (AppConfig.instance.skinStyle) {
      AppSkinStyle.kTemplateC => null,
      AppSkinStyle.kTemplateD => null,
      _ => context.colorTheme.textRegular,
    };
  }
}
