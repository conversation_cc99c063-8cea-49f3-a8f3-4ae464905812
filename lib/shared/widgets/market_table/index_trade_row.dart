import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/core/theme/app_themes.dart';
import 'package:gp_stock_app/core/utils/extensions.dart';
import 'package:gp_stock_app/core/utils/screen_util.dart';
import 'package:gp_stock_app/shared/models/stock/stock_response.dart';
import 'package:gp_stock_app/shared/widgets/flip_text.dart';
import 'package:gp_stock_app/shared/widgets/symbol/symbol_chip.dart';

class IndexTradeRow extends StatelessWidget {
  final VoidCallback onTap;
  final StockItem data;

  const IndexTradeRow({
    super.key,
    required this.data, required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8.gr),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 12.gh, horizontal: 4.gw),
          child: Row(
            children: [
              _buildNameAndSymbol(context),
              const Spacer(),
              _buildLatestPrice(context),
              20.horizontalSpace,
              _buildChangePercentage(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNameAndSymbol(BuildContext context) {
    return Expanded(
      flex: 3,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            data.name ?? 'N/A',
            style: switch (AppConfig.instance.skinStyle) {
              AppSkinStyle.kGP || AppSkinStyle.kTemplateD => context.textTheme.title,
              _ => context.textTheme.primary,
            }.w600.copyWith(
              overflow: TextOverflow.ellipsis,
            ),
            maxLines: 3,
          ),
          4.verticalSpace,
          Row(
            children: [
              SymbolChip(
                name: data.market ?? '',
                chipColor: context.theme.primaryColor,
              ),
              5.horizontalSpace,
              Flexible(
                child: Text(
                  data.symbol ?? 'N/A',
                  style: switch (AppConfig.instance.skinStyle) {
                    AppSkinStyle.kGP => context.textTheme.tertiary,
                    AppSkinStyle.kTemplateC => context.textTheme.regular,
                    AppSkinStyle.kTemplateD => context.textTheme.regular,

                    _ => context.textTheme.primary,
                  },
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLatestPrice(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: FlipText(
        data.latestPrice ?? 0,
        fractionDigits: 3,
        style: context.textTheme.primary.w500.copyWith(
          fontFamily: 'Akzidenz-Grotesk',
          color: (data.gain ?? 0.00).getValueColor(context),
          fontSize: 13.gsp,
        ),
      ),
    );
  }

  Widget _buildChangePercentage(BuildContext context) {
    final gainValue = data.chg ?? 0;
    final isPositive = gainValue > 0;

    return Align(
      alignment: Alignment.centerRight,
      child: Container(
        constraints: BoxConstraints(
          minHeight: 26.gw,
          minWidth: 40.gw, // Minimum width to ensure consistency
          maxWidth: 80.gw, // Maximum width to prevent overflow
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 8.gw,
          vertical: 6.gh,
        ),
        decoration: switch (AppConfig.instance.skinStyle) {

          AppSkinStyle.kTemplateC => BoxDecoration(
            border: Border.all(
              color: (data.gain ?? 0.00).getValueColor(context),
              width: 0.5,
            ),
            borderRadius: BorderRadius.circular(4.gr),
          ),
          _ => BoxDecoration(
            color: (data.gain ?? 0.00).getValueColor(context),
            borderRadius: BorderRadius.circular(4.gr),
          ),
        },
        child: Center(
          child: FlipText(
            gainValue.abs(),
            prefix: isPositive ? '+' : '-',
            suffix: '%',
            style: context.textTheme.primary.w700.copyWith(
              color: switch (AppConfig.instance.skinStyle) {
                AppSkinStyle.kTemplateC => (data.gain ?? 0.00).getValueColor(context),
                 _ => Colors.white
              },
              fontSize: 12.gsp, // Slightly smaller to fit better
              fontFamily: 'Akzidenz-Grotesk',
            ),
          ),
        ),
      ),
    );
  }
}
