import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/features/main/logic/main/main_cubit.dart';
import 'package:gp_stock_app/main_common.dart';
import 'package:gp_stock_app/my_app.dart';

mixin HideFloatButtonRouteAwareMixin<T extends StatefulWidget> on State<T> implements RouteAware {
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    getIt<MainCubit>().onChangeShowChatFloatWidget(true);
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  @override
  void didPop() {
    getIt<MainCubit>().onChangeShowChatFloatWidget(true);
  }

  @override
  void didPush() {
    getIt<MainCubit>().onChangeShowChatFloatWidget(false);
  }

  @override
  void didPopNext() {
    // Default empty implementation
  }

  @override
  void didPushNext() {
    // Default empty implementation
  }
}
