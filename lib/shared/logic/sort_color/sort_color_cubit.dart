import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:injectable/injectable.dart';

import '../../constants/enums.dart';

part 'sort_color_state.dart';

@singleton
class SortColorCubit extends HydratedCubit<SortColorState> {
  SortColorCubit() : super(const SortColorState(MarketColor.redUpGreenDown));

  void toggleMarketColor(MarketColor marketColor) {
    emit(state.copyWith(marketColor: marketColor));
  }

  void loadMarketColor(MarketColor marketColor) {
    emit(state.copyWith(marketColor: marketColor));
  }

  @override
  SortColorState fromJson(Map<String, dynamic> json) {
    final marketColor = MarketColor.values[json['marketColor']];
    return SortColorState(marketColor);
  }

  @override
  Map<String, dynamic> toJson(SortColorState state) {
    return {'marketColor': state.marketColor.index};
  }
}
