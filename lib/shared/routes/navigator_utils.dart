// navigator_service.dart

import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/core/utils/log.dart';
import 'package:gp_stock_app/shared/routes/route_tracker.dart';
import 'package:injectable/injectable.dart';
import 'routers.dart';

@singleton
class NavigatorService {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  Future push(String path,
      {bool replace = false,
      bool clearStack = false,
      TransitionType? transition,
      Duration? transitionDuration,
      Map<String, dynamic>? params,
      bool? opaque,
      Object? arguments}) {
    LogD("RouteTracker().getCurrentRouteName()>>> ${RouteTracker().getCurrentRouteName()}");
    LogD("path>>>$path");
    if (path == RouteTracker().getCurrentRouteName()) return Future.value(false);

    return Routes.router.navigateTo(
      navigatorKey.currentState!.context,
      _getParamsPath(path, params),
      opaque: opaque,
      replace: replace,
      clearStack: clearStack,
      transition: transition ?? TransitionType.cupertino,
      transitionDuration: transitionDuration,
      routeSettings: RouteSettings(
        arguments: arguments,
      ),
    );
  }

  Future pushReplace(String path,
      {TransitionType? transition,
        Duration? transitionDuration,
        Map<String, dynamic>? params,
        bool? opaque,
        Object? arguments}) {
    // RouteTracker().markClearStack(path);
    return Routes.router.navigateTo(
      navigatorKey.currentState!.context,
      _getParamsPath(path, params),
      opaque: opaque,
      replace: true,
      clearStack: true,
      transition: transition ?? TransitionType.none,
      transitionDuration: transitionDuration,
      routeSettings: RouteSettings(
        arguments: arguments,
      ),
    );
  }

  void pushAndKeepRoot(String path, {Object? arguments}) {
    navigatorKey.currentState?.pushNamedAndRemoveUntil(
      path,
          (route) => route.isFirst,
      arguments: arguments,
    );
  }


  void pop({Object? result}) {
    if (navigatorKey.currentState!.canPop()) {
      navigatorKey.currentState!.pop(result);
    } else {
      // 如果没有可以弹出的页面，你可以选择执行其他操作
      // 例如，显示一个提示信息或执行其他导航操作
      LogE('No more pages to pop');
    }
  }

  void popUntil(String path) {
    Navigator.popUntil(navigatorKey.currentState!.context, ModalRoute.withName(path));
  }

  void popUntilOrPush(String path, {bool clearStack = false}) {
    List<String> routeStack = RouteTracker().routeStack;
    if (routeStack.contains(path)) {
      popUntil(path);
    } else {
      push(path, clearStack: clearStack);
    }
  }

  void popToRoot() {
    navigatorKey.currentState?.popUntil((route) => route.isFirst);
  }


  String _getParamsPath(String path, Map<String, dynamic>? params) {
    if (params == null) {
      return path;
    }
    int index = 0;
    String query = "";
    for (var key in params.keys) {
      var value = "";
      if (params[key] is String) {
        value = Uri.encodeComponent(params[key]);
      } else {
        value = params[key].toString();
      }
      if (index == 0) {
        query = "?";
      } else {
        query = "$query&";
      }
      query += "$key=$value";
      index++;
    }
    path = path + query;
    return path;
  }

  void unFocus() {
    FocusManager.instance.primaryFocus?.unfocus();
  }
}
