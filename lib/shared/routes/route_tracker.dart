import 'package:flutter/material.dart';
import 'package:gp_stock_app/shared/routes/app_router.dart';

class RouteTracker extends NavigatorObserver {
  // 单例实现
  static final RouteTracker _instance = RouteTracker._internal();

  factory RouteTracker() => _instance;

  RouteTracker._internal();

  // 用于存储导航堆栈中的路由名称
  final List<String> _routeStack = [AppRouter.splash];

  List<String> get routeStack => List.unmodifiable(_routeStack);

  // 当一个新的路由被推入时调用
  @override
  void didPush(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPush(route, previousRoute);
    if (route.settings.name != null) {
      _routeStack.add(route.settings.name!);
    }
  }

  // 当一个路由被弹出时调用
  @override
  void didPop(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didPop(route, previousRoute);
    final poppedName = route.settings.name;
    if (poppedName != null) {
      _routeStack.remove(poppedName); // 精确删除
    }
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    super.didRemove(route, previousRoute);
    final removedName = route.settings.name;
    if (removedName != null) {
      _routeStack.remove(removedName);
    }
  }

  @override
  void didReplace({Route<dynamic>? newRoute, Route<dynamic>? oldRoute}) {
    super.didReplace(newRoute: newRoute, oldRoute: oldRoute);
    final oldName = oldRoute?.settings.name;
    final newName = newRoute?.settings.name;

    if (oldName != null) {
      _routeStack.remove(oldName);
    }
    if (newName != null) {
      _routeStack.add(newName);
    }
  }

  // 获取当前路由名称
  String? getCurrentRouteName() {
    return _routeStack.last;
  }

  // 获取上一级路由名称
  String? getPreviousRouteName() {
    if (_routeStack.length > 1) {
      return _routeStack[_routeStack.length - 2];
    }
    return null;
  }

  // 标记清空路由堆栈，并添加新路由
  void markClearStack(String newRouteName) {
    _routeStack.clear();
    _routeStack.add(newRouteName);
  }
}
