import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/features/home/<USER>/home_screen_enter.dart';
import 'app_router.dart';
import 'irouter_provider.dart';

class Routes {
  static final List<IRouterProvider> _listRouter = [];

  // FluroRouter路由库
  static final FluroRouter router = FluroRouter();

  static void initRoutes() {
    /// 指定路由跳转错误返回页
    router.notFoundHandler = Handler(handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
      // print("找不到页面>> $params");
      // return const NotFoundPage();
      return const HomeScreenEnter();

    });
    _listRouter.clear();

    /// 各自路由由各自模块管理，统一在此添加初始化
    _listRouter.add(AppRouter());

    /// 初始化路由
    void initRouter(IRouterProvider routerProvider) {
      routerProvider.initRouter(router);
    }

    _listRouter.forEach(initRouter);
  }
}
