#!/bin/bash

# ================================================================
# switch_flavor.sh
#
# 功能：
#   - 切换 Flutter 项目的 flavor、skin、color scheme
#   - 根据 flavor 自动匹配 skin / color scheme（若未手动传入）
#   - 合并翻译 JSON 文件
#
# 功能场景：
#   - 实现多环境、多皮肤自动切换
#   - 在 CI/CD 或开发者本地快速切换资源
#
# Usage 示例：
#   ./switch_flavor.sh --flavor gp
#   ./switch_flavor.sh --flavor gp --skin gp --color-scheme default
#   ./switch_flavor.sh --skin gp --color-scheme default
# ================================================================

# 初始化变量
FLAVOR=""
SKIN=""
COLOR_SCHEME=""

# --------------------------------------
# 解析命令行参数
# --------------------------------------
# Parse command-line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --flavor)
            if [ -z "$2" ] || [[ "$2" == --* ]]; then
                echo "Error: Missing value for --flavor"
                exit 1
            fi
            FLAVOR="$2"
            shift 2
            ;;
        --skin)
            if [ -z "$2" ] || [[ "$2" == --* ]]; then
                echo "Error: Missing value for --skin"
                exit 1
            fi
            SKIN="$2"
            shift 2
            ;;
        --color-scheme)
            if [ -z "$2" ] || [[ "$2" == --* ]]; then
                echo "Error: Missing value for --color-scheme"
                exit 1
            fi
            COLOR_SCHEME="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# --------------------------------------
# 切换 Flavor
# --------------------------------------
SCRIPT_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PROJECT_ROOT=$(realpath "$SCRIPT_DIR/..")
if [ -n "$FLAVOR" ]; then
    FLAVOR_DIR="$PROJECT_ROOT/assets/flavors/$FLAVOR"
    TARGET_DIR="$PROJECT_ROOT/assets"
    TRANSLATIONS_DIR="$TARGET_DIR/translations"
    FLAVOR_TRANSLATIONS_DIR="$FLAVOR_DIR/translations"

    # 检查 Flavor 是否存在
    if [ ! -d "$FLAVOR_DIR" ]; then
        echo "Error: Flavor '$FLAVOR' not found in assets/flavors/"
        echo "Available flavors: $(ls -1 assets/flavors | tr '\n' ' ')"
        exit 1
    fi

    # --------------------------------------
    # 根据 flavor 自动匹配 skin 和 color scheme
    # --------------------------------------
    # Automatically map skin/color-scheme by flavor
    case "$FLAVOR" in
        gp)
            DEFAULT_SKIN="gp"
            DEFAULT_COLOR_SCHEME="default"
            ;;
        pre)
            DEFAULT_SKIN="gp"
            DEFAULT_COLOR_SCHEME="default"
            ;;
        rsyp)
            DEFAULT_SKIN="gp"
            DEFAULT_COLOR_SCHEME="default"
            ;;
        yhxt)
            DEFAULT_SKIN="gp"
            DEFAULT_COLOR_SCHEME="default"
            ;;
        tempa)
            DEFAULT_SKIN="template_a"
            DEFAULT_COLOR_SCHEME="default"
            ;;
        bszb)
            DEFAULT_SKIN="template_d"
            DEFAULT_COLOR_SCHEME="default"
            ;;
        dyzb)
            DEFAULT_SKIN="template_c"
            DEFAULT_COLOR_SCHEME="default"
            ;;
        *)
            echo "Error: No default skin mapping found for flavor '$FLAVOR'."
            exit 1
            ;;
    esac

    # 如果未手动输入 skin，则使用默认
    if [ -z "$SKIN" ]; then
        SKIN="$DEFAULT_SKIN"
        echo "Auto-selected skin='$SKIN' for flavor '$FLAVOR'"
    fi

    if [ -z "$COLOR_SCHEME" ]; then
        COLOR_SCHEME="$DEFAULT_COLOR_SCHEME"
        echo "Auto-selected color_scheme='$COLOR_SCHEME' for flavor '$FLAVOR'"
    fi

    echo "-------------------------------------------"
    echo "Switching to flavor: $FLAVOR"
    echo "Copying files from $FLAVOR_DIR to $TARGET_DIR"
    echo "-------------------------------------------"

    # 拷贝 flavor 文件（除 translations）
    find "$FLAVOR_DIR" -type f -not -path "*/translations/*" | while read -r file; do
        rel_path="${file#$FLAVOR_DIR/}"
        target_dir="$TARGET_DIR/$(dirname "$rel_path")"
        mkdir -p "$target_dir"
        cp -v "$file" "$TARGET_DIR/$rel_path"
    done

    # --------------------------------------
    # 合并翻译文件
    # --------------------------------------
    echo "Updating translation files for flavor: $FLAVOR"

    # 检查 jq 是否安装
    if ! command -v jq &> /dev/null; then
        echo "Error: 'jq' is required for JSON manipulation but it's not installed."
        echo "Please install jq using your package manager:"
        echo "  - For Ubuntu/Debian: sudo apt-get install jq"
        echo "  - For MacOS: brew install jq"
        echo "  - For Windows with Chocolatey: choco install jq"
        exit 1
    fi

    for lang_file in "en-US.json" "zh-CN.json" "zh-TW.json"; do
        orig_file="$TRANSLATIONS_DIR/$lang_file"
        flavor_override_file="$FLAVOR_TRANSLATIONS_DIR/$lang_file"

        if [ -f "$orig_file" ] && [ -f "$flavor_override_file" ]; then
            echo "Merging $lang_file with $FLAVOR-specific translations"
            # 将 flavor 覆盖合并到原翻译文件中
            jq -s '.[0] * .[1]' "$orig_file" "$flavor_override_file" > "$orig_file.tmp"
            mv "$orig_file.tmp" "$orig_file"
        elif [ -f "$flavor_override_file" ]; then
            echo "Creating $lang_file with $FLAVOR-specific translations"
            mkdir -p "$TRANSLATIONS_DIR"
            cp "$flavor_override_file" "$orig_file"
        else
            echo "No flavor-specific translations found for $lang_file, skipping"
        fi
    done

    echo "Flavor switched to '$FLAVOR' successfully!"
    echo
fi

# --------------------------------------
# 切换 Skin + Color Scheme
# --------------------------------------
if [ -n "$SKIN" ] && [ -n "$COLOR_SCHEME" ]; then
    SKIN_DIR="$PROJECT_ROOT/assets/skins/$SKIN"
    COLOR_SCHEME_DIR="$SKIN_DIR/$COLOR_SCHEME"
    TARGET_DIR="assets"

    if [ ! -d "$SKIN_DIR" ]; then
        echo "Error: Skin '$SKIN' not found in assets/skins/"
        echo "Available skins: $(ls -1 assets/skins | tr '\n' ' ')"
        exit 1
    fi

    if [ ! -d "$COLOR_SCHEME_DIR" ]; then
        echo "Error: Color scheme '$COLOR_SCHEME' not found in $SKIN_DIR/"
        echo "Available color schemes for '$SKIN': $(ls -1 $SKIN_DIR | tr '\n' ' ')"
        exit 1
    fi

    echo "-------------------------------------------"
    echo "Switching to skin: $SKIN with color scheme: $COLOR_SCHEME"
    echo "Copying files from $COLOR_SCHEME_DIR to $TARGET_DIR"
    echo "-------------------------------------------"

    ASSET_FOLDERS=("icons" "images" "svg")

    for folder in "${ASSET_FOLDERS[@]}"; do
        source_folder="$COLOR_SCHEME_DIR/$folder"
        target_folder="$TARGET_DIR/$folder"

        if [ -d "$source_folder" ]; then
            echo "Copying $folder from $source_folder to $target_folder"
            mkdir -p "$target_folder"

            find "$source_folder" -type f | while read -r file; do
                rel_path="${file#$source_folder/}"
                target_subdir="$target_folder/$(dirname "$rel_path")"
                mkdir -p "$target_subdir"
                cp -v "$file" "$target_folder/$rel_path"
            done
        else
            echo "Warning: $folder directory not found in $COLOR_SCHEME_DIR, skipping"
        fi
    done

    echo "Skin switched to '$SKIN' with color scheme '$COLOR_SCHEME' successfully!"
elif [ -n "$SKIN" ] || [ -n "$COLOR_SCHEME" ]; then
    echo "Error: You must provide both --skin and --color-scheme to switch skin."
    exit 1
fi

echo
echo "🎉 All switches completed successfully!"
