import os
import sys

required_imports = [
    "import 'package:gp_stock_app/core/dependency_injection/injectable.dart';",
    "import 'package:gp_stock_app/shared/routes/navigator_utils.dart';"
]

def process_file(filepath):
    with open(filepath, "r", encoding="utf-8") as f:
        lines = f.readlines()
        content = "".join(lines)

    if "getIt<NavigatorService>" not in content:
        return

    has_imports = {imp: False for imp in required_imports}

    for line in lines:
        for imp in required_imports:
            if imp in line:
                has_imports[imp] = True

    to_add = [imp for imp, exists in has_imports.items() if not exists]

    if not to_add:
        return

    insert_index = 0
    for i, line in enumerate(lines):
        if line.strip().startswith("import"):
            insert_index = i + 1

    new_lines = lines[:insert_index] + [imp + "\n" for imp in to_add] + lines[insert_index:]

    with open(filepath, "w", encoding="utf-8") as f:
        f.writelines(new_lines)

    print(f"✅ 已更新: {filepath}, 添加 {len(to_add)} 条 import")

def scan_project(path):
    for root, dirs, files in os.walk(path):
        for file in files:
            if file.endswith(".dart"):
                process_file(os.path.join(root, file))

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("❌ 用法: python3 fix_navigator_imports.py <project_path>")
        sys.exit(1)

    project_dir = sys.argv[1]
    if not os.path.isdir(project_dir):
        print(f"❌ 无效路径: {project_dir}")
        sys.exit(1)

    scan_project(project_dir)
