plugins {
    id "com.android.application" version "8.7.0"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.gpmember.app"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = "17"
    }


    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "com.gpmember.app"
        // You can update the following values to match your application needs.
        // For more information, see: https://flutter.dev/to/review-gradle-config.
        minSdkVersion 23
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode
        versionName = flutter.versionName
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a' // Optional: excludes x86, x86_64 if not needed
        }
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias'] ?: "upload"
            keyPassword keystoreProperties['keyPassword'] ?: "gp@2025"
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : file("${System.getenv('HOME')}/jks/GP/key.jks")
            storePassword keystoreProperties['storePassword'] ?: "gp@2025"
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
        }
    }
}

flutter {
    source = "../.."
}



//gradle.taskGraph.whenReady { graph ->
//    println "🧭 All tasks in task graph:"
//    graph.allTasks.each {
//        println "👉 Executing task: ${it.path}"
//    }
//}























// ----- BEGIN flavorDimensions (autogenerated by flutter_flavorizr) -----
apply from: "flavorizr.gradle"
// ----- END flavorDimensions (autogenerated by flutter_flavorizr) -----