android {
    flavorDimensions += "flavor-type"

    productFlavors {
        pre {
            dimension "flavor-type"
            applicationId "com.gp.pre.stock"
            manifestPlaceholders.appScheme = "ffkoup"
            manifestPlaceholders.OPENINSTALL_APPKEY = "ffkoup"
            resValue "string", "app_name", "GP Pre"
        }
        gp {
            dimension "flavor-type"
            applicationId "com.gp.stock"
            manifestPlaceholders.appScheme = "vgyl58"
            manifestPlaceholders.OPENINSTALL_APPKEY = "vgyl58"
            resValue "string", "app_name", "GP Stock"
        }
        rsyp {
            dimension "flavor-type"
            applicationId "com.rsyp.stock"
            manifestPlaceholders.appScheme = "n8x1cr"
            manifestPlaceholders.OPENINSTALL_APPKEY = "n8x1cr"
            resValue "string", "app_name", "荣顺优配"
        }
        yhxt {
            dimension "flavor-type"
            applicationId "com.yhxt.stock"
            manifestPlaceholders.appScheme = "gf4rmg"
            manifestPlaceholders.OPENINSTALL_APPKEY = "gf4rmg"
            resValue "string", "app_name", "沅和信投"
        }
        tempa {
            dimension "flavor-type"
            applicationId "com.tempa.stock"
            manifestPlaceholders.appScheme = "k0qslv"
            manifestPlaceholders.OPENINSTALL_APPKEY = "k0qslv"
            resValue "string", "app_name", "tempa"
        }
        bszb {
            dimension "flavor-type"
            applicationId "com.tempd.stock"
            manifestPlaceholders.appScheme = "vhtdvx"
            manifestPlaceholders.OPENINSTALL_APPKEY = "vhtdvx"
            resValue "string", "app_name", "宝石资本"
        }
        dyzb {
            dimension "flavor-type"
            applicationId "com.dyzb.stock"
            manifestPlaceholders.appScheme = "isijsu"
            manifestPlaceholders.OPENINSTALL_APPKEY = "isijsu"
            resValue "string", "app_name", "德盈资本"
        }
    }
}