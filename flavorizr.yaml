
app:
  android:
    flavorDimensions: "flavor-type"
  ios:
    instructions:
      # 替换 Info.plist 为每个 flavor 独立文件
      - path: "ios/Runner.xcodeproj/project.pbxproj"
        regex: 'INFOPLIST_FILE = Runner/Info.plist;'
        replace: 'INFOPLIST_FILE = Runner/Info-{{flavorName}}.plist;'
      # 替换 entitlements
      - path: "ios/Runner.xcodeproj/project.pbxproj"
        regex: 'CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;'
        replace: 'CODE_SIGN_ENTITLEMENTS = Runner/Runner-{{flavorName}}.entitlements;'

flavors:
  pre:
    app:
      name: "GP Pre"
    android:
      applicationId: "com.gp.pre.stock"
      icon: "assets/flavors/pre/ic_launcher.png"
      customConfig:
        manifestPlaceholders.appScheme: = "ffkoup"
        manifestPlaceholders.OPENINSTALL_APPKEY: = "ffkoup"
    ios:
      bundleId: "com.gp.pre.stock"
      icon: "assets/flavors/pre/ic_launcher.png"
      variables:
        OPEN_INSTALL_KEY:
          value: "ffkoup"
  gp:
    app:
      name: "GP Stock"
    android:
      applicationId: "com.gp.stock"
      icon: "assets/flavors/gp/ic_launcher.png"
      customConfig:
        manifestPlaceholders.appScheme: = "vgyl58"
        manifestPlaceholders.OPENINSTALL_APPKEY: = "vgyl58"
    ios:
      bundleId: "com.gp.stock"
      icon: "assets/flavors/gp/ic_launcher.png"
      variables:
        OPEN_INSTALL_KEY:
          value: "vgyl58"

  rsyp:
    app:
      name: "荣顺优配"
    android:
      applicationId: "com.rsyp.stock"
      icon: "assets/flavors/rsyp/ic_launcher.png"
      customConfig:
        manifestPlaceholders.appScheme: = "n8x1cr"
        manifestPlaceholders.OPENINSTALL_APPKEY: = "n8x1cr"
    ios:
      bundleId: "com.rsyp.stock"
      icon: "assets/flavors/rsyp/ic_launcher.png"
      variables:
        OPEN_INSTALL_KEY:
          value: "n8x1cr"

  yhxt:
    app:
      name: "沅和信投"
    android:
      applicationId: "com.yhxt.stock"
      icon: "assets/flavors/yhxt/ic_launcher.png"
      customConfig:
        manifestPlaceholders.appScheme: = "gf4rmg"
        manifestPlaceholders.OPENINSTALL_APPKEY: = "gf4rmg"
    ios:
      bundleId: "com.yhxt.stock"
      icon: "assets/flavors/yhxt/ic_launcher.png"
      variables:
        OPEN_INSTALL_KEY:
          value: "gf4rmg"

  tempa:
    app:
      name: "tempa"
    android:
      applicationId: "com.tempa.stock"
      icon: "assets/flavors/tempa/ic_launcher.png"
      customConfig:
        manifestPlaceholders.appScheme: = "k0qslv"
        manifestPlaceholders.OPENINSTALL_APPKEY: = "k0qslv"
    ios:
      bundleId: "com.tempa.stock"
      icon: "assets/flavors/tempa/ic_launcher.png"
      variables:
        OPEN_INSTALL_KEY:
          value: "k0qslv"

  bszb:
    app:
      name: "宝石资本"
    android:
      applicationId: "com.tempd.stock"
      icon: "assets/flavors/bszb/ic_launcher.png"
      customConfig:
        manifestPlaceholders.appScheme: = "vhtdvx"
        manifestPlaceholders.OPENINSTALL_APPKEY: = "vhtdvx"
    ios:
      bundleId: "com.tempd.stock"
      icon: "assets/flavors/bszb/ic_launcher.png"
      variables:
        OPEN_INSTALL_KEY:
          value: "vhtdvx"

  dyzb:
    app:
      name: "德盈资本"
    android:
      applicationId: "com.dyzb.stock"
      icon: "assets/flavors/dyzb/ic_launcher.png"
      customConfig:
        manifestPlaceholders.appScheme: = "isijsu"
        manifestPlaceholders.OPENINSTALL_APPKEY: = "isijsu"
    ios:
      bundleId: "com.dyzb.stock"
      icon: "assets/flavors/dyzb/ic_launcher.png"
      variables:
        OPEN_INSTALL_KEY:
          value: "isijsu"
